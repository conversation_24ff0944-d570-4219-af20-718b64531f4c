
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  role: 'role',
  status: 'status',
  technicalLevel: 'technicalLevel',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio',
  socialLinks: 'socialLinks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLogin: 'lastLogin'
};

exports.Prisma.EntityTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  websiteUrl: 'websiteUrl',
  entityTypeId: 'entityTypeId',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  foundedYear: 'foundedYear',
  status: 'status',
  socialLinks: 'socialLinks',
  submitterId: 'submitterId',
  legacyId: 'legacyId',
  reviewCount: 'reviewCount',
  avgRating: 'avgRating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  scrapedReviewSentimentScore: 'scrapedReviewSentimentScore',
  scrapedReviewCount: 'scrapedReviewCount',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  locationSummary: 'locationSummary',
  refLink: 'refLink',
  affiliateStatus: 'affiliateStatus'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  parentCategoryId: 'parentCategoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityTagScalarFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId'
};

exports.Prisma.EntityCategoryScalarFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedAt: 'assignedAt'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  rating: 'rating',
  title: 'title',
  reviewText: 'reviewText',
  status: 'status',
  helpfulnessScore: 'helpfulnessScore',
  moderatorUserId: 'moderatorUserId',
  moderatedAt: 'moderatedAt',
  moderationNotes: 'moderationNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReviewVoteScalarFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId',
  vote: 'vote',
  createdAt: 'createdAt'
};

exports.Prisma.EntityDetailsToolScalarFieldEnum = {
  entityId: 'entityId',
  programmingLanguages: 'programmingLanguages',
  frameworks: 'frameworks',
  libraries: 'libraries',
  integrations: 'integrations',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  targetAudience: 'targetAudience',
  learningCurve: 'learningCurve',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  mobileSupport: 'mobileSupport',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  trialAvailable: 'trialAvailable',
  demoAvailable: 'demoAvailable',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  hasFreeTier: 'hasFreeTier',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsAgencyScalarFieldEnum = {
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industryFocus: 'industryFocus',
  targetClientSize: 'targetClientSize',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo'
};

exports.Prisma.EntityDetailsContentCreatorScalarFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  focusAreas: 'focusAreas',
  followerCount: 'followerCount',
  exampleContentUrl: 'exampleContentUrl'
};

exports.Prisma.EntityDetailsCommunityScalarFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  memberCount: 'memberCount',
  focusTopics: 'focusTopics',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl'
};

exports.Prisma.EntityDetailsNewsletterScalarFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  mainTopics: 'mainTopics',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  subscriberCount: 'subscriberCount'
};

exports.Prisma.EntityDetailsCourseScalarFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  skillLevel: 'skillLevel',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  enrollmentCount: 'enrollmentCount',
  certificateAvailable: 'certificateAvailable'
};

exports.Prisma.UserSavedEntityScalarFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedTagScalarFieldEnum = {
  userId: 'userId',
  tagId: 'tagId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedCategoryScalarFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId',
  createdAt: 'createdAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  actionType: 'actionType',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId',
  timestamp: 'timestamp',
  details: 'details'
};

exports.Prisma.UserNotificationSettingsScalarFieldEnum = {
  userId: 'userId',
  emailNewsletter: 'emailNewsletter',
  emailNewEntityInFollowedCategory: 'emailNewEntityInFollowedCategory',
  emailNewEntityInFollowedTag: 'emailNewEntityInFollowedTag',
  emailNewReviewOnSavedEntity: 'emailNewReviewOnSavedEntity',
  emailUpdatesOnSavedEntity: 'emailUpdatesOnSavedEntity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BadgeTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  criteriaDetails: 'criteriaDetails',
  scope: 'scope',
  isAutoGranted: 'isAutoGranted',
  isManualGranted: 'isManualGranted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserBadgeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityBadgeScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityDetailsDatasetScalarFieldEnum = {
  entityId: 'entityId',
  format: 'format',
  sourceUrl: 'sourceUrl',
  license: 'license',
  sizeInBytes: 'sizeInBytes',
  description: 'description',
  accessNotes: 'accessNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsResearchPaperScalarFieldEnum = {
  entityId: 'entityId',
  publicationDate: 'publicationDate',
  doi: 'doi',
  authors: 'authors',
  abstract: 'abstract',
  journalOrConference: 'journalOrConference',
  publicationUrl: 'publicationUrl',
  citationCount: 'citationCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsSoftwareScalarFieldEnum = {
  entityId: 'entityId',
  repositoryUrl: 'repositoryUrl',
  licenseType: 'licenseType',
  programmingLanguages: 'programmingLanguages',
  platformCompatibility: 'platformCompatibility',
  currentVersion: 'currentVersion',
  releaseDate: 'releaseDate',
  hasFreeTier: 'hasFreeTier',
  useCases: 'useCases',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  integrations: 'integrations',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsModelScalarFieldEnum = {
  entityId: 'entityId',
  modelArchitecture: 'modelArchitecture',
  parametersCount: 'parametersCount',
  trainingDataset: 'trainingDataset',
  performanceMetrics: 'performanceMetrics',
  modelUrl: 'modelUrl',
  license: 'license',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsProjectReferenceScalarFieldEnum = {
  entityId: 'entityId',
  projectStatus: 'projectStatus',
  sourceCodeUrl: 'sourceCodeUrl',
  liveDemoUrl: 'liveDemoUrl',
  technologies: 'technologies',
  projectGoals: 'projectGoals',
  contributors: 'contributors',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsServiceProviderScalarFieldEnum = {
  entityId: 'entityId',
  serviceAreas: 'serviceAreas',
  caseStudiesUrl: 'caseStudiesUrl',
  consultationBookingUrl: 'consultationBookingUrl',
  industrySpecializations: 'industrySpecializations',
  companySizeFocus: 'companySizeFocus',
  hourlyRateRange: 'hourlyRateRange',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsInvestorScalarFieldEnum = {
  entityId: 'entityId',
  investmentFocusAreas: 'investmentFocusAreas',
  portfolioUrl: 'portfolioUrl',
  typicalInvestmentSize: 'typicalInvestmentSize',
  investmentStages: 'investmentStages',
  contactEmail: 'contactEmail',
  preferredCommunication: 'preferredCommunication',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsEventScalarFieldEnum = {
  entityId: 'entityId',
  eventType: 'eventType',
  startDate: 'startDate',
  endDate: 'endDate',
  location: 'location',
  registrationUrl: 'registrationUrl',
  speakerList: 'speakerList',
  agendaUrl: 'agendaUrl',
  price: 'price',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsJobScalarFieldEnum = {
  entityId: 'entityId',
  jobTitle: 'jobTitle',
  companyName: 'companyName',
  locationType: 'locationType',
  salaryRange: 'salaryRange',
  applicationUrl: 'applicationUrl',
  jobDescription: 'jobDescription',
  experienceLevel: 'experienceLevel',
  employmentType: 'employmentType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsGrantScalarFieldEnum = {
  entityId: 'entityId',
  grantingInstitution: 'grantingInstitution',
  eligibilityCriteria: 'eligibilityCriteria',
  applicationDeadline: 'applicationDeadline',
  fundingAmount: 'fundingAmount',
  applicationUrl: 'applicationUrl',
  grantFocusArea: 'grantFocusArea',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsBountyScalarFieldEnum = {
  entityId: 'entityId',
  bountyIssuer: 'bountyIssuer',
  rewardAmount: 'rewardAmount',
  requirements: 'requirements',
  submissionDeadline: 'submissionDeadline',
  platformUrl: 'platformUrl',
  difficultyLevel: 'difficultyLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsHardwareScalarFieldEnum = {
  entityId: 'entityId',
  hardwareType: 'hardwareType',
  specifications: 'specifications',
  manufacturer: 'manufacturer',
  releaseDate: 'releaseDate',
  priceRange: 'priceRange',
  datasheetUrl: 'datasheetUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsNewsScalarFieldEnum = {
  entityId: 'entityId',
  publicationDate: 'publicationDate',
  sourceName: 'sourceName',
  articleUrl: 'articleUrl',
  author: 'author',
  summary: 'summary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsBookScalarFieldEnum = {
  entityId: 'entityId',
  authorNames: 'authorNames',
  isbn: 'isbn',
  publisher: 'publisher',
  publicationYear: 'publicationYear',
  pageCount: 'pageCount',
  summary: 'summary',
  purchaseUrl: 'purchaseUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsPodcastScalarFieldEnum = {
  entityId: 'entityId',
  hostNames: 'hostNames',
  averageEpisodeLength: 'averageEpisodeLength',
  mainTopics: 'mainTopics',
  listenUrl: 'listenUrl',
  frequency: 'frequency',
  primaryLanguage: 'primaryLanguage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsPlatformScalarFieldEnum = {
  entityId: 'entityId',
  platformType: 'platformType',
  keyServices: 'keyServices',
  documentationUrl: 'documentationUrl',
  pricingModel: 'pricingModel',
  slaUrl: 'slaUrl',
  supportedRegions: 'supportedRegions',
  hasFreeTier: 'hasFreeTier',
  useCases: 'useCases',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  integrations: 'integrations',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityFeatureScalarFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId',
  assignedAt: 'assignedAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio'
};

exports.Prisma.EntityTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  websiteUrl: 'websiteUrl',
  entityTypeId: 'entityTypeId',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  submitterId: 'submitterId',
  legacyId: 'legacyId',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  locationSummary: 'locationSummary',
  refLink: 'refLink'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  parentCategoryId: 'parentCategoryId'
};

exports.Prisma.TagOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityTagOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId'
};

exports.Prisma.EntityCategoryOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId'
};

exports.Prisma.ReviewOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  title: 'title',
  reviewText: 'reviewText',
  moderatorUserId: 'moderatorUserId',
  moderationNotes: 'moderationNotes'
};

exports.Prisma.ReviewVoteOrderByRelevanceFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId'
};

exports.Prisma.EntityDetailsToolOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  customizationLevel: 'customizationLevel',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsAgencyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo'
};

exports.Prisma.EntityDetailsContentCreatorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  exampleContentUrl: 'exampleContentUrl'
};

exports.Prisma.EntityDetailsCommunityOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl'
};

exports.Prisma.EntityDetailsNewsletterOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName'
};

exports.Prisma.EntityDetailsCourseOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl'
};

exports.Prisma.UserSavedEntityOrderByRelevanceFieldEnum = {
  userId: 'userId',
  entityId: 'entityId'
};

exports.Prisma.UserFollowedTagOrderByRelevanceFieldEnum = {
  userId: 'userId',
  tagId: 'tagId'
};

exports.Prisma.UserFollowedCategoryOrderByRelevanceFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId'
};

exports.Prisma.UserActivityLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId'
};

exports.Prisma.UserNotificationSettingsOrderByRelevanceFieldEnum = {
  userId: 'userId'
};

exports.Prisma.BadgeTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  criteriaDetails: 'criteriaDetails'
};

exports.Prisma.UserBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedByUserId: 'grantedByUserId',
  notes: 'notes'
};

exports.Prisma.EntityDetailsDatasetOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  format: 'format',
  sourceUrl: 'sourceUrl',
  license: 'license',
  description: 'description',
  accessNotes: 'accessNotes'
};

exports.Prisma.EntityDetailsResearchPaperOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  doi: 'doi',
  abstract: 'abstract',
  journalOrConference: 'journalOrConference',
  publicationUrl: 'publicationUrl'
};

exports.Prisma.EntityDetailsSoftwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  repositoryUrl: 'repositoryUrl',
  licenseType: 'licenseType',
  currentVersion: 'currentVersion',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsModelOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  modelUrl: 'modelUrl',
  license: 'license'
};

exports.Prisma.EntityDetailsProjectReferenceOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  projectStatus: 'projectStatus',
  sourceCodeUrl: 'sourceCodeUrl',
  liveDemoUrl: 'liveDemoUrl',
  projectGoals: 'projectGoals'
};

exports.Prisma.EntityDetailsServiceProviderOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  caseStudiesUrl: 'caseStudiesUrl',
  consultationBookingUrl: 'consultationBookingUrl',
  companySizeFocus: 'companySizeFocus',
  hourlyRateRange: 'hourlyRateRange'
};

exports.Prisma.EntityDetailsInvestorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  portfolioUrl: 'portfolioUrl',
  typicalInvestmentSize: 'typicalInvestmentSize',
  contactEmail: 'contactEmail',
  preferredCommunication: 'preferredCommunication'
};

exports.Prisma.EntityDetailsEventOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  eventType: 'eventType',
  location: 'location',
  registrationUrl: 'registrationUrl',
  agendaUrl: 'agendaUrl',
  price: 'price'
};

exports.Prisma.EntityDetailsJobOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  jobTitle: 'jobTitle',
  companyName: 'companyName',
  locationType: 'locationType',
  salaryRange: 'salaryRange',
  applicationUrl: 'applicationUrl',
  jobDescription: 'jobDescription',
  experienceLevel: 'experienceLevel',
  employmentType: 'employmentType'
};

exports.Prisma.EntityDetailsGrantOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  grantingInstitution: 'grantingInstitution',
  eligibilityCriteria: 'eligibilityCriteria',
  fundingAmount: 'fundingAmount',
  applicationUrl: 'applicationUrl',
  grantFocusArea: 'grantFocusArea'
};

exports.Prisma.EntityDetailsBountyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  bountyIssuer: 'bountyIssuer',
  rewardAmount: 'rewardAmount',
  requirements: 'requirements',
  platformUrl: 'platformUrl',
  difficultyLevel: 'difficultyLevel'
};

exports.Prisma.EntityDetailsHardwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  hardwareType: 'hardwareType',
  manufacturer: 'manufacturer',
  priceRange: 'priceRange',
  datasheetUrl: 'datasheetUrl'
};

exports.Prisma.EntityDetailsNewsOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  sourceName: 'sourceName',
  articleUrl: 'articleUrl',
  author: 'author',
  summary: 'summary'
};

exports.Prisma.EntityDetailsBookOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  isbn: 'isbn',
  publisher: 'publisher',
  summary: 'summary',
  purchaseUrl: 'purchaseUrl'
};

exports.Prisma.EntityDetailsPodcastOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  averageEpisodeLength: 'averageEpisodeLength',
  listenUrl: 'listenUrl',
  frequency: 'frequency',
  primaryLanguage: 'primaryLanguage'
};

exports.Prisma.EntityDetailsPlatformOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  platformType: 'platformType',
  documentationUrl: 'documentationUrl',
  slaUrl: 'slaUrl',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityFeatureOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId'
};

exports.Prisma.FeatureOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  MODERATOR: 'MODERATOR'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  SUSPENDED: 'SUSPENDED',
  DELETED: 'DELETED'
};

exports.TechnicalLevel = exports.$Enums.TechnicalLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.EntityStatus = exports.$Enums.EntityStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  REJECTED: 'REJECTED',
  NEEDS_REVISION: 'NEEDS_REVISION',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED'
};

exports.AffiliateStatus = exports.$Enums.AffiliateStatus = {
  NONE: 'NONE',
  APPLIED: 'APPLIED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.ReviewStatus = exports.$Enums.ReviewStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.LearningCurve = exports.$Enums.LearningCurve = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.PricingModel = exports.$Enums.PricingModel = {
  FREE: 'FREE',
  FREEMIUM: 'FREEMIUM',
  SUBSCRIPTION: 'SUBSCRIPTION',
  PAY_PER_USE: 'PAY_PER_USE',
  ONE_TIME_PURCHASE: 'ONE_TIME_PURCHASE',
  CONTACT_SALES: 'CONTACT_SALES',
  OPEN_SOURCE: 'OPEN_SOURCE'
};

exports.PriceRange = exports.$Enums.PriceRange = {
  FREE: 'FREE',
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  ENTERPRISE: 'ENTERPRISE'
};

exports.SkillLevel = exports.$Enums.SkillLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.ActionType = exports.$Enums.ActionType = {
  VIEW_ENTITY: 'VIEW_ENTITY',
  CLICK_ENTITY_LINK: 'CLICK_ENTITY_LINK',
  SAVE_ENTITY: 'SAVE_ENTITY',
  UNSAVE_ENTITY: 'UNSAVE_ENTITY',
  SUBMIT_REVIEW: 'SUBMIT_REVIEW',
  VOTE_REVIEW: 'VOTE_REVIEW',
  FOLLOW_TAG: 'FOLLOW_TAG',
  UNFOLLOW_TAG: 'UNFOLLOW_TAG',
  FOLLOW_CATEGORY: 'FOLLOW_CATEGORY',
  UNFOLLOW_CATEGORY: 'UNFOLLOW_CATEGORY',
  SEARCH: 'SEARCH',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  SIGNUP: 'SIGNUP',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  GRANT_BADGE: 'GRANT_BADGE',
  REVOKE_BADGE: 'REVOKE_BADGE'
};

exports.BadgeScope = exports.$Enums.BadgeScope = {
  USER: 'USER',
  ENTITY: 'ENTITY'
};

exports.Prisma.ModelName = {
  User: 'User',
  EntityType: 'EntityType',
  Entity: 'Entity',
  Category: 'Category',
  Tag: 'Tag',
  EntityTag: 'EntityTag',
  EntityCategory: 'EntityCategory',
  Review: 'Review',
  ReviewVote: 'ReviewVote',
  EntityDetailsTool: 'EntityDetailsTool',
  EntityDetailsAgency: 'EntityDetailsAgency',
  EntityDetailsContentCreator: 'EntityDetailsContentCreator',
  EntityDetailsCommunity: 'EntityDetailsCommunity',
  EntityDetailsNewsletter: 'EntityDetailsNewsletter',
  EntityDetailsCourse: 'EntityDetailsCourse',
  UserSavedEntity: 'UserSavedEntity',
  UserFollowedTag: 'UserFollowedTag',
  UserFollowedCategory: 'UserFollowedCategory',
  UserActivityLog: 'UserActivityLog',
  UserNotificationSettings: 'UserNotificationSettings',
  BadgeType: 'BadgeType',
  UserBadge: 'UserBadge',
  EntityBadge: 'EntityBadge',
  EntityDetailsDataset: 'EntityDetailsDataset',
  EntityDetailsResearchPaper: 'EntityDetailsResearchPaper',
  EntityDetailsSoftware: 'EntityDetailsSoftware',
  EntityDetailsModel: 'EntityDetailsModel',
  EntityDetailsProjectReference: 'EntityDetailsProjectReference',
  EntityDetailsServiceProvider: 'EntityDetailsServiceProvider',
  EntityDetailsInvestor: 'EntityDetailsInvestor',
  EntityDetailsEvent: 'EntityDetailsEvent',
  EntityDetailsJob: 'EntityDetailsJob',
  EntityDetailsGrant: 'EntityDetailsGrant',
  EntityDetailsBounty: 'EntityDetailsBounty',
  EntityDetailsHardware: 'EntityDetailsHardware',
  EntityDetailsNews: 'EntityDetailsNews',
  EntityDetailsBook: 'EntityDetailsBook',
  EntityDetailsPodcast: 'EntityDetailsPodcast',
  EntityDetailsPlatform: 'EntityDetailsPlatform',
  EntityFeature: 'EntityFeature',
  Feature: 'Feature'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
