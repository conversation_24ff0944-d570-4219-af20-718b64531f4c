// tsconfig.json
{
    "compilerOptions": {
      "module": "commonjs",
      "declaration": true,
      "removeComments": true,
      "emitDecoratorMetadata": true,
      "experimentalDecorators": true,
      "allowSyntheticDefaultImports": true,
      "target": "ES2021",
      "sourceMap": true,
      "outDir": "./dist",
      "baseUrl": "./",
      "paths": {
        "@generated-prisma": ["generated/prisma"],
        "@generated-prisma/*": ["generated/prisma/*"]
      },
      "incremental": true,
      "skipLibCheck": true,
      "strictNullChecks": true,
      "noImplicitAny": true,
      "strictBindCallApply": true,
      "forceConsistentCasingInFileNames": true,
      "noFallthroughCasesInSwitch": true,
      "esModuleInterop": true,
      "moduleResolution": "node"
    },
    "exclude": ["node_modules", "dist"]
}