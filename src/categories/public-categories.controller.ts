import { Controller, Get } from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CategoryResponseDto } from './dto/category-response.dto';
import { Category as CategoryModel } from '../../generated/prisma';

@ApiTags('Public - Categories')
@Controller('categories')
export class PublicCategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  private mapToResponseDto(category: CategoryModel): CategoryResponseDto {
    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description, // Assuming CategoryResponseDto includes description
      iconUrl: category.iconUrl,
      parentCategoryId: category.parentCategoryId, // Assuming CategoryResponseDto includes parentCategoryId
      createdAt: category.createdAt, // Assuming CategoryResponseDto includes createdAt
      updatedAt: category.updatedAt, // Assuming CategoryResponseDto includes updatedAt
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all public categories' })
  @ApiResponse({ status: 200, description: 'List of all public categories.', type: [CategoryResponseDto] })
  async findAll(): Promise<CategoryResponseDto[]> {
    const categories = await this.categoriesService.findAllPublic();
    return categories.map(cat => this.mapToResponseDto(cat));
  }
} 