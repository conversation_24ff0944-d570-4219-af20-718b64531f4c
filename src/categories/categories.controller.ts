import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, ParseUUIDPipe, NotFoundException } from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { AdminGuard } from '../auth/guards/admin.guard'; // Assuming AdminGuard is in this path
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { CategoryResponseDto } from './dto/category-response.dto'; // Import response DTO
// import { AdminCategoryListResponseDto } from '../admin/categories/dto/admin-category-list-response.dto'; // Commented out as service might not support pagination yet
import { Category as CategoryModel } from '../../generated/prisma'; // Import Prisma model

@ApiTags('Admin - Categories')
@ApiBearerAuth() // Indicates that JWT authentication is required for this controller
@UseGuards(AdminGuard) // Protect all routes in this controller with AdminGuard
@Controller('admin/categories') // Base path for all routes in this controller
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiBody({ type: CreateCategoryDto })
  @ApiResponse({ status: 201, description: 'The category has been successfully created.', type: CategoryResponseDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async create(@Body() createCategoryDto: CreateCategoryDto): Promise<CategoryResponseDto> {
    const category = await this.categoriesService.create(createCategoryDto);
    return this.mapToCategoryResponseDto(category);
  }

  @Get()
  @ApiOperation({ summary: 'Get all categories' })
  // @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number for pagination.', example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page.', example: 10 })
  @ApiResponse({ status: 200, description: 'List of all categories.', type: [CategoryResponseDto] }) // Returns an array
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async findAll(): Promise<CategoryResponseDto[]> { // Returns an array
    const categories = await this.categoriesService.findAll(); // Assuming this returns CategoryModel[] or compatible structure
    // If categoriesService.findAll() returns { data: CategoryModel[], ... }, adjust here:
    // const dataToMap = Array.isArray(categories) ? categories : categories.data;
    // return dataToMap.map(this.mapToCategoryResponseDto);
    // For now, assuming it's an array or an object with a 'data' property that is an array.
    // The linter error from previous step suggests it IS an object with a data property.
    if (categories && Array.isArray((categories as any).data)) {
        return (categories as any).data.map(this.mapToCategoryResponseDto);
    } else if (Array.isArray(categories)) {
        return categories.map(this.mapToCategoryResponseDto);
    }
    return []; // Fallback if the structure is unexpected
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a category by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Category ID (UUID)', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'The found category.', type: CategoryResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Category not found.' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<CategoryResponseDto> {
    const category = await this.categoriesService.findOne(id);
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found.`);
    }
    return this.mapToCategoryResponseDto(category);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a category by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Category ID (UUID)', format: 'uuid' })
  @ApiBody({ type: UpdateCategoryDto })
  @ApiResponse({ status: 200, description: 'The category has been successfully updated.', type: CategoryResponseDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Category not found.' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateCategoryDto: UpdateCategoryDto): Promise<CategoryResponseDto> {
    const category = await this.categoriesService.update(id, updateCategoryDto);
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found after update attempt.`);
    }
    return this.mapToCategoryResponseDto(category);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a category by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Category ID (UUID)', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'The category has been successfully deleted.' /* No specific DTO needed here, or a simple message DTO */ })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Category not found.' })
  remove(@Param('id') id: string) {
    return this.categoriesService.remove(id);
  }

  // Helper method to map Prisma Category to CategoryResponseDto
  private mapToCategoryResponseDto(category: CategoryModel): CategoryResponseDto {
    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      iconUrl: category.iconUrl,
      parentCategoryId: category.parentCategoryId,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };
  }
} 