import { Injectable, NotFoundException, ConflictException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service'; // Assuming PrismaService is in a prisma module
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto'; // Reverted path
import { Category, Prisma } from 'generated/prisma'; // Or your specific Prisma Category type
import { generateSlug } from '../utils/slug.utils'; // Assuming a utility for slug generation
import { Logger } from '@nestjs/common';

@Injectable()
export class CategoriesService {
  private readonly logger = new Logger(CategoriesService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
    const slug = createCategoryDto.slug || generateSlug(createCategoryDto.name);

    try {
      const category = await this.prisma.category.create({
        data: {
          name: createCategoryDto.name,
          slug: slug,
          description: createCategoryDto.description,
          iconUrl: createCategoryDto.icon_url,
          ...(createCategoryDto.parent_category_id && {
            parentCategory: {
              connect: { id: createCategoryDto.parent_category_id },
            },
          }),
        },
      });
      return category;
    } catch (error) {
      this.logger.error(
        `Caught error during CategoriesService.create. Error type: ${error?.constructor?.name}, Name: ${error?.name}, Message: ${error?.message}`,
        // error // Log the full error object for deep inspection if needed during debugging
      );
      if (error && typeof error === 'object') {
        // Check for PrismaClientKnownRequestError (has a 'code' property starting with 'P')
        if ('code' in error && typeof error.code === 'string' && error.code.startsWith('P')) {
          this.logger.warn(
            `Re-throwing PrismaClientKnownRequestError from CategoriesService (Code: ${error.code}) for global filter. Target: ${error.meta?.target}`,
          );
          throw error; // Re-throw
        } 
        // Check for PrismaClientValidationError (has a 'name' property)
        // Prisma.PrismaClientValidationError is a type, not a value for instanceof at runtime sometimes, so check by name.
        else if (error.name === 'PrismaClientValidationError') { 
          this.logger.warn(
            `Re-throwing PrismaClientValidationError from CategoriesService for global filter.`,
          );
          throw error; // Re-throw
        }
      }
      // Fallback for other errors or if the checks above didn't identify it as a Prisma error to be re-thrown
      this.logger.error(
        `Unexpected error in CategoriesService.create for category '${createCategoryDto.name}'. Not identified as Prisma error for re-throw. Original error: ${error?.message}`,
        error?.stack,
      );
      throw new InternalServerErrorException('Could not create category due to an unexpected server issue.');
    }
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ data: Category[], count: number, totalPages: number, currentPage: number }> {
    const skip = (page - 1) * limit;
    try {
      const [categories, totalCount] = await this.prisma.$transaction([
        this.prisma.category.findMany({
          skip,
          take: limit,
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.category.count(),
      ]);
      return {
        data: categories,
        count: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      };
    } catch (error) {
      console.error('Error fetching all categories:', error);
      throw new InternalServerErrorException('Could not fetch categories.');
    }
  }

  async findAllPublic(): Promise<Category[]> { // New method for public, non-paginated access
    try {
      return await this.prisma.category.findMany({
        orderBy: {
          name: 'asc',
        },
      });
    } catch (error) {
      this.logger.error('Error fetching all public categories:', error.stack);
      throw new InternalServerErrorException('Could not fetch public categories.');
    }
  }

  async findOne(id: string): Promise<Category> { // ID is UUID (string)
    const category = await this.prisma.category.findUnique({
      where: { id },
    });
    if (!category) {
      throw new NotFoundException(`Category with ID "${id}" not found`);
    }
    return category;
  }

  async update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<Category> { // ID is UUID (string)
    await this.findOne(id); // Ensures category exists

    const dataToUpdate: Prisma.CategoryUpdateInput = {};
    if (updateCategoryDto.name !== undefined) dataToUpdate.name = updateCategoryDto.name;
    if (updateCategoryDto.description !== undefined) dataToUpdate.description = updateCategoryDto.description;
    if (updateCategoryDto.icon_url !== undefined) dataToUpdate.iconUrl = updateCategoryDto.icon_url;
    
    // Handle parentCategoryId explicitly to allow setting it to null or connecting to a new parent
    if (updateCategoryDto.hasOwnProperty('parent_category_id')) {
      if (updateCategoryDto.parent_category_id === null) {
        dataToUpdate.parentCategory = { disconnect: true };
      } else if (updateCategoryDto.parent_category_id) {
        dataToUpdate.parentCategory = { connect: { id: updateCategoryDto.parent_category_id } };
      }
    }

    // Slug generation logic
    if (updateCategoryDto.slug) {
      dataToUpdate.slug = generateSlug(updateCategoryDto.slug);
    } else if (updateCategoryDto.name) {
      // If name is updated and slug is not, regenerate slug from new name
      // This requires fetching the current category to compare names if we want to avoid unnecessary slug changes.
      // For simplicity now, if name changes, slug might change. Adjust if slug needs to be more permanent.
      dataToUpdate.slug = generateSlug(updateCategoryDto.name);
    }

    try {
      return await this.prisma.category.update({
        where: { id },
        data: dataToUpdate,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') { // Unique constraint (name or slug)
          throw new ConflictException(`Category with this name or slug already exists.`);
        }
        if (error.code === 'P2025') { // Record to update not found
          throw new NotFoundException(`Category with ID "${id}" not found to update.`);
        }
      }
      console.error(`Error updating category ${id}:`, error);
      throw new InternalServerErrorException('Could not update category.');
    }
  }

  async remove(id: string): Promise<Category> { // ID is UUID (string)
    await this.findOne(id); // Ensure category exists

    try {
      const subcategoriesCount = await this.prisma.category.count({
        where: { parentCategoryId: id }, // Corrected field name
      });
      if (subcategoriesCount > 0) {
        throw new ConflictException(`Category with ID "${id}" has subcategories. Please delete or reassign them first.`);
      }

      // Placeholder for checking entity associations - replace with actual logic later
      // const associatedEntitiesCount = await this.prisma.entityCategory.count({ where: { categoryId: id } });
      // if (associatedEntitiesCount > 0) {
      //   throw new ConflictException(`Category with ID "${id}" is associated with entities and cannot be deleted.`);
      // }

      return await this.prisma.category.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(`Category with ID "${id}" not found to delete.`);
        }
      }
      if (error instanceof ConflictException) throw error; // Re-throw our custom conflict exceptions
      console.error(`Error removing category ${id}:`, error);
      throw new InternalServerErrorException('Could not delete category.');
    }
  }
} 