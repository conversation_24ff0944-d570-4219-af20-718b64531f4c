import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AdminReviewsController } from './admin-reviews.controller';
import { ReviewsService } from './reviews.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { mockPrismaService } from '../test/mocks/prisma.service.mock';
import { UpdateReviewStatusDto } from './dto/update-review-status.dto';
import { Review, ReviewStatus, User as UserModel, UserRole, UserStatus } from '@generated-prisma';

describe('AdminReviewsController (Integration)', () => {
  let app: INestApplication;
  let reviewsService: ReviewsService;

  const mockAdminUser: UserModel = {
    id: 'admin-123',
    authUserId: 'auth-admin-123',
    email: '<EMAIL>',
    username: 'admin',
    displayName: 'Admin User',
    profilePictureUrl: null,
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockReview: Review = {
    id: 'review-123',
    userId: 'user-123',
    entityId: 'entity-123',
    rating: 5,
    title: 'Great tool!',
    reviewText: 'This is an excellent tool for AI development.',
    status: ReviewStatus.PENDING,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    helpfulCount: 0,
    reportedCount: 0,
    moderatorNotes: null,
    moderatedAt: null,
    moderatedBy: null,
  };

  const mockReviewWithRelations = {
    ...mockReview,
    user: {
      id: 'user-123',
      username: 'testuser',
      email: '<EMAIL>',
    },
    entity: {
      id: 'entity-123',
      name: 'Test Entity',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminReviewsController],
      providers: [
        ReviewsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockAdminUser;
          return true;
        }),
      })
      .overrideGuard(AdminGuard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    app = module.createNestApplication();
    
    // Apply global validation pipe like in main.ts
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    reviewsService = module.get<ReviewsService>(ReviewsService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('PATCH /admin/reviews/:id/status', () => {
    const reviewId = 'review-123';
    const updateStatusDto: UpdateReviewStatusDto = {
      status: ReviewStatus.APPROVED,
    };

    it('should update review status successfully', async () => {
      const updatedReview = {
        ...mockReviewWithRelations,
        status: ReviewStatus.APPROVED,
      };
      jest.spyOn(reviewsService, 'updateReviewStatus').mockResolvedValue(updatedReview as any);

      const response = await request(app.getHttpServer())
        .patch(`/admin/reviews/${reviewId}/status`)
        .send(updateStatusDto)
        .expect(200);

      expect(response.body.status).toBe(ReviewStatus.APPROVED);
      expect(reviewsService.updateReviewStatus).toHaveBeenCalledWith(
        reviewId,
        ReviewStatus.APPROVED,
        mockAdminUser,
      );
    });

    it('should validate status enum', async () => {
      const invalidStatusDto = {
        status: 'INVALID_STATUS',
      };

      await request(app.getHttpServer())
        .patch(`/admin/reviews/${reviewId}/status`)
        .send(invalidStatusDto)
        .expect(400);
    });

    it('should require status field', async () => {
      const emptyDto = {};

      await request(app.getHttpServer())
        .patch(`/admin/reviews/${reviewId}/status`)
        .send(emptyDto)
        .expect(400);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .patch('/admin/reviews/invalid-uuid/status')
        .send(updateStatusDto)
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminReviewsController],
        providers: [
          ReviewsService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .patch(`/admin/reviews/${reviewId}/status`)
        .send(updateStatusDto)
        .expect(403);

      await testApp.close();
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...updateStatusDto,
        rating: 5, // Should be stripped
        maliciousField: 'should be removed',
      };

      const updatedReview = {
        ...mockReviewWithRelations,
        status: ReviewStatus.APPROVED,
      };
      jest.spyOn(reviewsService, 'updateReviewStatus').mockResolvedValue(updatedReview as any);

      await request(app.getHttpServer())
        .patch(`/admin/reviews/${reviewId}/status`)
        .send(dtoWithExtraFields)
        .expect(200);

      expect(reviewsService.updateReviewStatus).toHaveBeenCalledWith(
        reviewId,
        ReviewStatus.APPROVED,
        mockAdminUser,
      );
    });

    it('should handle different status values', async () => {
      const testCases = [
        ReviewStatus.APPROVED,
        ReviewStatus.REJECTED,
        ReviewStatus.PENDING,
      ];

      for (const status of testCases) {
        const dto = { status };
        const updatedReview = { ...mockReviewWithRelations, status };
        
        jest.spyOn(reviewsService, 'updateReviewStatus').mockResolvedValue(updatedReview as any);

        await request(app.getHttpServer())
          .patch(`/admin/reviews/${reviewId}/status`)
          .send(dto)
          .expect(200);

        expect(reviewsService.updateReviewStatus).toHaveBeenCalledWith(
          reviewId,
          status,
          mockAdminUser,
        );
      }
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(reviewsService, 'updateReviewStatus').mockRejectedValue(
        new Error('Review not found'),
      );

      await request(app.getHttpServer())
        .patch(`/admin/reviews/${reviewId}/status`)
        .send(updateStatusDto)
        .expect(500);
    });
  });

  describe('DELETE /admin/reviews/:id', () => {
    const reviewId = 'review-123';

    it('should delete review successfully as admin', async () => {
      jest.spyOn(reviewsService, 'adminDeleteReview').mockResolvedValue(undefined);

      await request(app.getHttpServer())
        .delete(`/admin/reviews/${reviewId}`)
        .expect(204);

      expect(reviewsService.adminDeleteReview).toHaveBeenCalledWith(reviewId, mockAdminUser);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .delete('/admin/reviews/invalid-uuid')
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminReviewsController],
        providers: [
          ReviewsService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .delete(`/admin/reviews/${reviewId}`)
        .expect(403);

      await testApp.close();
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(reviewsService, 'adminDeleteReview').mockRejectedValue(
        new Error('Review not found'),
      );

      await request(app.getHttpServer())
        .delete(`/admin/reviews/${reviewId}`)
        .expect(500);
    });

    it('should require JWT authentication first', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminReviewsController],
        providers: [
          ReviewsService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .delete(`/admin/reviews/${reviewId}`)
        .expect(403);

      await testApp.close();
    });
  });
});
