import {
  Controller,
  UseGuards,
  Patch,
  Param,
  Body,
  Delete,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  Req,
} from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { UpdateReviewStatusDto } from './dto/update-review-status.dto';
import { User as UserModel } from '../../generated/prisma'; 
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Reviews Management (Admin)')
@Controller('admin/reviews') 
@UseGuards(JwtAuthGuard, AdminGuard) 
@ApiBearerAuth()
export class AdminReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Patch(':id/status')
  @ApiOperation({ summary: 'Admin: Update review status' })
  @ApiResponse({
    status: 200,
    description: 'The review status has been successfully updated.',
    type: Object, 
  })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Review not found.' })
  async updateReviewStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateReviewStatusDto: UpdateReviewStatusDto,
    @Req() req: any, 
  ) {
    return this.reviewsService.updateReviewStatus(
      id,
      updateReviewStatusDto.status,
      req.user as UserModel,
    );
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Admin: Delete any review' })
  @ApiResponse({ status: 204, description: 'Review successfully deleted by admin.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Review not found.' })
  async adminDeleteReview(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: any,
  ) {
    return this.reviewsService.adminDeleteReview(id, req.user as UserModel);
  }
} 