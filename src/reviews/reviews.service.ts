import { Injectable, NotFoundException, ConflictException, InternalServerErrorException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { Review, ReviewStatus, Prisma, User as UserModel } from 'generated/prisma';
import { UpdateReviewDto } from './dto/update-review.dto';
import { Logger } from '@nestjs/common';

@Injectable()
export class ReviewsService {
  private readonly logger = new Logger(ReviewsService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createReview(
    userId: string,
    entityId: string,
    createReviewDto: CreateReviewDto,
  ): Promise<Review> {
    const { rating, title, reviewText } = createReviewDto;

    // 1. Check if the entity exists
    const entity = await this.prisma.entity.findUnique({
      where: { id: entityId },
      select: { id: true }, // Select minimal data
    });
    if (!entity) {
      throw new NotFoundException(`Entity with ID "${entityId}" not found.`);
    }

    // 2. Check if the user has already reviewed this entity
    const existingReview = await this.prisma.review.findUnique({
      where: {
        userId_entityId: {
          userId,
          entityId,
        },
      },
    });

    if (existingReview) {
      throw new ConflictException('You have already reviewed this entity.');
    }

    // 3. Create the review
    try {
      const newReview = await this.prisma.review.create({
        data: {
          userId,
          entityId,
          rating,
          title,
          reviewText,
          status: ReviewStatus.PENDING, // Default to PENDING
        },
      });
      return newReview;
    } catch (error) {
      // Log detailed error in real application
      console.error('Error creating review:', error);
      throw new InternalServerErrorException('Could not submit review.');
    }
  }

  async updateUserReview(
    reviewId: string,
    userId: string,
    updateReviewDto: UpdateReviewDto,
  ): Promise<Review> {
    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    });

    if (!review) {
      throw new NotFoundException(`Review with ID "${reviewId}" not found.`);
    }

    if (review.userId !== userId) {
      throw new ForbiddenException('You are not authorized to update this review.');
    }

    if (review.status !== ReviewStatus.PENDING) {
      throw new ForbiddenException('Only pending reviews can be updated.');
      // Or, allow updates for a short window after approval if that's a feature
    }

    try {
      const updatedReview = await this.prisma.review.update({
        where: { id: reviewId },
        data: {
          ...updateReviewDto,
          updatedAt: new Date(), // Explicitly set updatedAt if not auto-updated by Prisma on every change
        },
      });
      return updatedReview;
    } catch (error) {
      console.error(`Error updating review ${reviewId}:`, error);
      throw new InternalServerErrorException('Could not update review.');
    }
  }

  async deleteUserReview(reviewId: string, userId: string): Promise<void> {
    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    });

    if (!review) {
      // If the review doesn't exist, consider it successfully deleted (idempotency)
      // Or throw NotFoundException if strict check is preferred.
      // For now, let's be idempotent for DELETE.
      return;
    }

    if (review.userId !== userId) {
      throw new ForbiddenException('You are not authorized to delete this review.');
    }

    // Business rule: Maybe only PENDING reviews can be deleted by users?
    // Or any review they own? For now, allow deleting any owned review.
    // If an APPROVED review is deleted, the trigger for entity stats should handle it.

    try {
      await this.prisma.review.delete({
        where: { id: reviewId },
      });
    } catch (error) {
      // P2025: Record to delete not found. Already handled by the check above for idempotency.
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return; 
      }
      console.error(`Error deleting review ${reviewId}:`, error);
      throw new InternalServerErrorException('Could not delete review.');
    }
  }

  async getApprovedReviewsForEntity(
    entityId: string,
    listReviewsDto: ListReviewsDto,
  ): Promise<{ data: Partial<Review & { user: { username: string | null, profilePictureUrl: string | null, id: string } }>[]; total: number; page: number; limit: number }> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = Prisma.SortOrder.desc } = listReviewsDto;
    const skip = (page - 1) * limit;

    // 1. Check if the entity exists (optional, but good practice)
    const entityExists = await this.prisma.entity.findUnique({
      where: { id: entityId },
      select: { id: true },
    });
    if (!entityExists) {
      throw new NotFoundException(`Entity with ID "${entityId}" not found.`);
    }

    try {
      const [reviews, total] = await this.prisma.$transaction([
        this.prisma.review.findMany({
          where: {
            entityId,
            status: ReviewStatus.APPROVED,
          },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                profilePictureUrl: true,
              },
            },
          },
          orderBy: {
            [sortBy]: sortOrder,
          },
          skip,
          take: limit,
        }),
        this.prisma.review.count({
          where: {
            entityId,
            status: ReviewStatus.APPROVED,
          },
        }),
      ]);

      return {
        data: reviews,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error fetching approved reviews:', error);
      throw new InternalServerErrorException('Could not fetch reviews.');
    }
  }

  async updateReviewStatus(
    reviewId: string,
    newStatus: ReviewStatus,
    adminUser: UserModel, // For logging or additional checks if needed
  ): Promise<Review> { // Assuming Review is the Prisma type
    this.logger.log(
      `Admin ${adminUser.email} (ID: ${adminUser.id}) attempting to update review ${reviewId} to status ${newStatus}`,
    );

    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    });

    if (!review) {
      this.logger.warn(`Review with ID ${reviewId} not found for status update.`);
      throw new NotFoundException(`Review with ID ${reviewId} not found.`);
    }

    // The trigger 'trigger_update_entity_stats_on_review_change' on the 'reviews' table
    // will automatically handle updating 'review_count' and 'avg_rating' on the parent 'entities' table
    // when the 'status' of a review is changed (especially to/from 'APPROVED').
    const updatedReview = await this.prisma.review.update({
      where: { id: reviewId },
      data: {
        status: newStatus,
        // We could also add an admin_moderator_id here if the schema supports it
        // and if we want to track which admin changed the status.
        // moderator_id: adminUser.id, 
        // moderation_timestamp: new Date(),
        updatedAt: new Date(), // Corrected from updated_at to updatedAt
      },
      include: {
        user: true,
        entity: true, // Include related entity if useful for response
      },
    });

    this.logger.log(
      `Review ${reviewId} status successfully updated to ${newStatus} by admin ${adminUser.email}`,
    );
    return updatedReview;
  }

  async adminDeleteReview(reviewId: string, adminUser: UserModel): Promise<void> {
    this.logger.log(
      `Admin ${adminUser.email} (ID: ${adminUser.id}) attempting to delete review ${reviewId}`,
    );

    const review = await this.prisma.review.findUnique({
      where: { id: reviewId },
    });

    if (!review) {
      // For admin delete, it might be better to throw NotFoundException
      // as an admin would expect the resource to exist if they are deleting it.
      this.logger.warn(`Review with ID ${reviewId} not found for admin deletion.`);
      throw new NotFoundException(`Review with ID ${reviewId} not found.`);
    }

    // The trigger 'trigger_update_entity_stats_on_review_change' on the 'reviews' table
    // will automatically handle updating 'review_count' and 'avg_rating' on the parent 'entities' table
    // when a review is deleted.
    try {
      await this.prisma.review.delete({
        where: { id: reviewId },
      });
      this.logger.log(
        `Review ${reviewId} successfully deleted by admin ${adminUser.email}`,
      );
    } catch (error) {
      // Handle cases where delete might fail unexpectedly, though Prisma typically handles 'not found' gracefully.
      // P2025: Record to delete not found. This is covered by the check above.
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        this.logger.warn(`Review with ID ${reviewId} was already deleted (P2025).`);
        throw new NotFoundException(`Review with ID ${reviewId} not found or already deleted.`);
      }
      this.logger.error(
        `Error deleting review ${reviewId} by admin ${adminUser.email}:`, 
        error.stack || error
      );
      throw new InternalServerErrorException('Could not delete review.');
    }
  }
} 