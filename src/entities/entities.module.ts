import { Modu<PERSON> } from '@nestjs/common';
import { EntitiesService } from './entities.service';
import { EntitiesController } from './entities.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { CategoriesModule } from '../categories/categories.module';
import { TagsModule } from '../tags/tags.module';

@Module({
  imports: [
    PrismaModule, 
    AuthModule, 
    CategoriesModule, // For potential validation of category_ids
    TagsModule,       // For potential validation of tag_ids
  ],
  controllers: [EntitiesController],
  providers: [EntitiesService],
  exports: [EntitiesService], // If other modules might need EntitiesService
})
export class EntitiesModule {} 