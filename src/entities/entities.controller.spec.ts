import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { EntitiesController } from './entities.controller';
import { EntitiesService } from './entities.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { mockPrismaService } from '../test/mocks/prisma.service.mock';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { ListEntitiesDto } from './dto/list-entities.dto';
import { Entity, EntityStatus, EntityType, UserRole, User as UserModel, Prisma } from '@generated-prisma';

describe('EntitiesController (Integration)', () => {
  let app: INestApplication;
  let entitiesService: EntitiesService;

  const mockUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: 'ACTIVE' as any,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockAdminUser: UserModel = {
    ...mockUser,
    id: 'admin-123',
    role: UserRole.ADMIN,
  };

  const mockEntityType: EntityType = {
    id: 'type-123',
    name: 'AI Tool',
    description: 'AI Tool type',
    slug: 'ai-tool',
    iconUrl: 'https://example.com/icon.png',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockEntity: Entity = {
    id: 'entity-123',
    name: 'Test Entity',
    websiteUrl: 'https://example.com',
    entityTypeId: 'type-123',
    shortDescription: 'A test entity',
    description: 'A longer description',
    logoUrl: 'https://example.com/logo.png',
    documentationUrl: 'https://example.com/docs',
    contactUrl: 'https://example.com/contact',
    privacyPolicyUrl: 'https://example.com/privacy',
    foundedYear: 2023,
    status: EntityStatus.APPROVED,
    socialLinks: { twitter: 'https://twitter.com/test' },
    submitterId: 'user-123',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    metaTitle: null,
    metaDescription: null,
    employeeCountRange: null,
    fundingStage: null,
    locationSummary: null,
    refLink: null,
    affiliateStatus: null,
    scrapedReviewSentimentLabel: null,
    scrapedReviewSentimentScore: null,
    scrapedReviewCount: null,
    averageRating: null,
    totalReviews: 0,
    lastReviewedAt: null,
    featuredScore: null,
    trendingScore: null,
    qualityScore: null,
    popularityScore: null,
    lastScrapedAt: null,
    scrapingEnabled: false,
    adminNotes: null,
    rejectionReason: null,
    approvedAt: null,
    approvedBy: null,
    lastModifiedBy: null,
    slug: 'test-entity',
    searchVector: null,
  };

  const mockEntityWithRelations = {
    ...mockEntity,
    entityType: mockEntityType,
    submitter: mockUser,
    entityCategories: [],
    entityTags: [],
    entityFeatures: [],
    reviews: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EntitiesController],
      providers: [
        EntitiesService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockUser; // Default to regular user
          return true;
        }),
      })
      .overrideGuard(AdminGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockAdminUser; // Set admin user for admin endpoints
          return true;
        }),
      })
      .compile();

    app = module.createNestApplication();
    
    // Apply global validation pipe like in main.ts
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    entitiesService = module.get<EntitiesService>(EntitiesService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('POST /entities', () => {
    const createEntityDto: CreateEntityDto = {
      name: 'Test Entity',
      website_url: 'https://example.com',
      entity_type_id: 'type-123',
      short_description: 'A test entity',
      description: 'A longer description',
      logo_url: 'https://example.com/logo.png',
      category_ids: ['cat-1', 'cat-2'],
      tag_ids: ['tag-1', 'tag-2'],
      feature_ids: ['feat-1'],
    };

    it('should create an entity successfully', async () => {
      jest.spyOn(entitiesService, 'create').mockResolvedValue(mockEntityWithRelations as any);

      const response = await request(app.getHttpServer())
        .post('/entities')
        .send(createEntityDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(createEntityDto.name);
      expect(response.body.websiteUrl).toBe(createEntityDto.website_url);
      expect(entitiesService.create).toHaveBeenCalledWith(createEntityDto, mockUser);
    });

    it('should return 400 for invalid input data', async () => {
      const invalidDto = {
        name: '', // Empty name should fail validation
        website_url: 'invalid-url', // Invalid URL
        entity_type_id: 'invalid-uuid', // Invalid UUID
      };

      await request(app.getHttpServer())
        .post('/entities')
        .send(invalidDto)
        .expect(400);
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteDto = {
        name: 'Test Entity',
        // Missing required fields
      };

      await request(app.getHttpServer())
        .post('/entities')
        .send(incompleteDto)
        .expect(400);
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...createEntityDto,
        extraField: 'should be removed',
        maliciousField: 'also removed',
      };

      jest.spyOn(entitiesService, 'create').mockResolvedValue(mockEntityWithRelations as any);

      await request(app.getHttpServer())
        .post('/entities')
        .send(dtoWithExtraFields)
        .expect(201);

      // Verify that only whitelisted fields were passed to the service
      expect(entitiesService.create).toHaveBeenCalledWith(
        expect.not.objectContaining({
          extraField: expect.anything(),
          maliciousField: expect.anything(),
        }),
        mockUser,
      );
    });

    it('should require authentication', async () => {
      // Override guard to return false
      const module = await Test.createTestingModule({
        controllers: [EntitiesController],
        providers: [
          EntitiesService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .post('/entities')
        .send(createEntityDto)
        .expect(403);

      await testApp.close();
    });
  });

  describe('GET /entities', () => {
    const mockPaginatedResult = {
      data: [mockEntityWithRelations],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    it('should return paginated entities with default parameters', async () => {
      jest.spyOn(entitiesService, 'findAll').mockResolvedValue(mockPaginatedResult as any);

      const response = await request(app.getHttpServer())
        .get('/entities')
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body).toHaveProperty('totalPages');
      expect(response.body.data).toHaveLength(1);
      expect(entitiesService.findAll).toHaveBeenCalledWith(expect.any(Object));
    });

    it('should handle query parameters correctly', async () => {
      jest.spyOn(entitiesService, 'findAll').mockResolvedValue(mockPaginatedResult as any);

      await request(app.getHttpServer())
        .get('/entities')
        .query({
          page: 2,
          limit: 5,
          status: EntityStatus.APPROVED,
          searchTerm: 'test',
          sortBy: 'name',
          sortOrder: 'asc',
        })
        .expect(200);

      expect(entitiesService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 2,
          limit: 5,
          status: EntityStatus.APPROVED,
          searchTerm: 'test',
          sortBy: 'name',
          sortOrder: 'asc',
        }),
      );
    });

    it('should handle array query parameters (categoryIds, tagIds)', async () => {
      jest.spyOn(entitiesService, 'findAll').mockResolvedValue(mockPaginatedResult as any);

      await request(app.getHttpServer())
        .get('/entities')
        .query({
          categoryIds: ['cat-1', 'cat-2'],
          tagIds: ['tag-1', 'tag-2'],
          featureIds: ['feat-1'],
        })
        .expect(200);

      expect(entitiesService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          categoryIds: ['cat-1', 'cat-2'],
          tagIds: ['tag-1', 'tag-2'],
          featureIds: ['feat-1'],
        }),
      );
    });

    it('should return empty results when no entities match', async () => {
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
      jest.spyOn(entitiesService, 'findAll').mockResolvedValue(emptyResult as any);

      const response = await request(app.getHttpServer())
        .get('/entities')
        .expect(200);

      expect(response.body.data).toHaveLength(0);
      expect(response.body.total).toBe(0);
    });

    it('should validate query parameter types', async () => {
      await request(app.getHttpServer())
        .get('/entities')
        .query({
          page: 'invalid', // Should be number
          limit: 'invalid', // Should be number
        })
        .expect(400);
    });
  });

  describe('GET /entities/:id', () => {
    it('should return a single entity by id', async () => {
      jest.spyOn(entitiesService, 'findOne').mockResolvedValue(mockEntityWithRelations as any);

      const response = await request(app.getHttpServer())
        .get(`/entities/${mockEntity.id}`)
        .expect(200);

      expect(response.body).toHaveProperty('id', mockEntity.id);
      expect(response.body).toHaveProperty('name', mockEntity.name);
      expect(response.body).toHaveProperty('entityType');
      expect(response.body).toHaveProperty('submitter');
      expect(entitiesService.findOne).toHaveBeenCalledWith(mockEntity.id);
    });

    it('should return 404 when entity not found', async () => {
      jest.spyOn(entitiesService, 'findOne').mockResolvedValue(null);

      await request(app.getHttpServer())
        .get('/entities/non-existent-id')
        .expect(404);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .get('/entities/invalid-uuid')
        .expect(400);
    });
  });

  describe('PATCH /entities/:id', () => {
    const updateEntityDto: UpdateEntityDto = {
      name: 'Updated Entity',
      short_description: 'Updated description',
      category_ids: ['cat-3'],
    };

    it('should update an entity successfully', async () => {
      const updatedEntity = { ...mockEntityWithRelations, ...updateEntityDto };

      jest.spyOn(entitiesService, 'update').mockResolvedValue(updatedEntity as any);
      jest.spyOn(entitiesService, 'findOne').mockResolvedValue(updatedEntity as any);

      const response = await request(app.getHttpServer())
        .patch(`/entities/${mockEntity.id}`)
        .send(updateEntityDto)
        .expect(200);

      expect(response.body.name).toBe(updateEntityDto.name);
      expect(entitiesService.update).toHaveBeenCalledWith(mockEntity.id, updateEntityDto, mockUser);
    });

    it('should return 404 when entity to update not found', async () => {
      jest.spyOn(entitiesService, 'update').mockRejectedValue(
        new Error('Entity not found'),
      );

      await request(app.getHttpServer())
        .patch('/entities/non-existent-id')
        .send(updateEntityDto)
        .expect(500); // Service error becomes 500
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .patch('/entities/invalid-uuid')
        .send(updateEntityDto)
        .expect(400);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [EntitiesController],
        providers: [
          EntitiesService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .patch(`/entities/${mockEntity.id}`)
        .send(updateEntityDto)
        .expect(403);

      await testApp.close();
    });

    it('should validate input data', async () => {
      const invalidDto = {
        name: '', // Empty name should fail
        website_url: 'invalid-url', // Invalid URL
      };

      await request(app.getHttpServer())
        .patch(`/entities/${mockEntity.id}`)
        .send(invalidDto)
        .expect(400);
    });
  });

  describe('DELETE /entities/:id', () => {
    it('should delete an entity successfully as admin', async () => {
      jest.spyOn(entitiesService, 'remove').mockResolvedValue(undefined);

      await request(app.getHttpServer())
        .delete(`/entities/${mockEntity.id}`)
        .expect(200);

      expect(entitiesService.remove).toHaveBeenCalledWith(mockEntity.id, mockAdminUser);
    });

    it('should return 404 when entity to delete not found', async () => {
      jest.spyOn(entitiesService, 'remove').mockRejectedValue(
        new Error('Entity not found'),
      );

      await request(app.getHttpServer())
        .delete('/entities/non-existent-id')
        .expect(500);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .delete('/entities/invalid-uuid')
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [EntitiesController],
        providers: [
          EntitiesService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .delete(`/entities/${mockEntity.id}`)
        .expect(403);

      await testApp.close();
    });
  });
});
