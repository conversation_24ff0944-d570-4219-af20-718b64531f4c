import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SoftwareDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'c1d2e3f4-g5h6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'URL to the software repository (e.g., GitHub, GitLab)..', example: 'https://github.com/user/repo' })
  repositoryUrl?: string | null;

  @ApiPropertyOptional({ description: 'Type of license for the software (e.g., MIT, GPLv3).', example: 'MIT' })
  licenseType?: string | null;

  @ApiPropertyOptional({
    description: 'Programming languages used or supported by the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Python', 'JavaScript', 'C++'],
  })
  programmingLanguages?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Platforms the software is compatible with.',
    type: 'array',
    items: { type: 'string' },
    example: ['Windows', 'Linux', 'macOS', 'Web'],
  })
  platformCompatibility?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Current version of the software.', example: '1.2.3' })
  currentVersion?: string | null;

  @ApiPropertyOptional({ description: 'Release date of the current version.', example: '2023-04-01', type: String, format: 'date' })
  releaseDate?: Date | null;

  @ApiProperty({ description: 'Timestamp of when the software details were created', example: '2023-03-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the software details', example: '2023-03-10T10:00:00.000Z' })
  updatedAt: Date;
} 