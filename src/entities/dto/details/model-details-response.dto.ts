import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ModelDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'd1e2f3g4-h5i6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Architecture of the model (e.g., Transformer, CNN, RNN).', example: 'Transformer' })
  modelArchitecture?: string | null;

  @ApiPropertyOptional({ description: 'Number of parameters in the model.', example: 175000000000, type: 'integer', format: 'int64' })
  parametersCount?: bigint | null;

  @ApiPropertyOptional({ description: 'Information about the dataset used for training the model.', example: 'Trained on BookCorpus and Wikipedia.' })
  trainingDataset?: string | null;

  @ApiPropertyOptional({
    description: 'Performance metrics of the model.',
    type: 'object',
    additionalProperties: { type: 'number' }, // Allows any key with a number value
    example: { accuracy: 0.95, f1_score: 0.92, perplexity: 8.5 },
  })
  performanceMetrics?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'URL to download or access the model (e.g., Hugging Face Hub)..', example: 'https://huggingface.co/org/model' })
  modelUrl?: string | null;

  @ApiPropertyOptional({ description: 'License of the model (e.g., Apache 2.0, MIT).', example: 'Apache 2.0' })
  license?: string | null;

  @ApiProperty({ description: 'Timestamp of when the model details were created', example: '2023-04-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the model details', example: '2023-04-10T10:00:00.000Z' })
  updatedAt: Date;
} 