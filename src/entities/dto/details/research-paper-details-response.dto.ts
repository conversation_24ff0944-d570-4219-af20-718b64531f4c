import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ResearchPaperDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'b1c2d3e4-f5g6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Date of publication.', example: '2023-05-15', type: String, format: 'date' })
  publicationDate?: Date | null;

  @ApiPropertyOptional({ description: 'Digital Object Identifier (DOI) of the research paper.', example: '10.1000/xyz123' })
  doi?: string | null;

  @ApiPropertyOptional({
    description: 'List of authors.',
    type: 'array',
    items: { type: 'string' },
    example: ['Dr. <PERSON>', 'Dr. <PERSON>'],
  })
  authors?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Abstract of the research paper.', example: 'This paper explores advanced techniques in machine learning...' })
  abstract?: string | null;

  @ApiPropertyOptional({ description: 'Name of the journal or conference where the paper was published.', example: 'Journal of AI Research' })
  journalOrConference?: string | null;

  @ApiPropertyOptional({ description: 'URL to the publication (e.g., on arXiv, publisher site)..', example: 'https://arxiv.org/abs/2305.12345' })
  publicationUrl?: string | null;

  @ApiPropertyOptional({ description: 'Number of citations the paper has received.', example: 42, type: 'integer' })
  citationCount?: number | null;

  @ApiProperty({ description: 'Timestamp of when the research paper details were created', example: '2023-02-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the research paper details', example: '2023-02-10T10:00:00.000Z' })
  updatedAt: Date;
} 