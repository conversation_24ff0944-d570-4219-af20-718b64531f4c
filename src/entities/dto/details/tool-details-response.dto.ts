import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TechnicalLevel, LearningCurve, PricingModel, PriceRange } from '../../../../generated/prisma'; // Adjust path as needed

export class ToolDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'd4e5f6g7-h8i9-0123-4567-890123abcdef' })
  entityId: string;

  @ApiPropertyOptional({ enum: TechnicalLevel, description: 'Recommended technical level for using the tool.', example: TechnicalLevel.INTERMEDIATE })
  technicalLevel?: TechnicalLevel | null;

  @ApiPropertyOptional({ enum: LearningCurve, description: 'Perceived learning curve for the tool.', example: LearningCurve.MEDIUM })
  learningCurve?: LearningCurve | null;

  @ApiPropertyOptional({
    description: 'Target audience for the tool (e.g., developers, designers, marketers).',
    type: 'object',
    example: { developers: true, dataScientists: true },
    additionalProperties: true, // Added for flexible object structure
  })
  targetAudience?: any | null; // Prisma Json type

  @ApiProperty({ description: 'Indicates if the tool has an API.', example: true, default: false })
  hasApi: boolean;

  @ApiPropertyOptional({ description: 'URL to the API documentation.', example: 'https://example.com/api/docs' })
  apiDocumentationUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL to an API sandbox environment.', example: 'https://sandbox.example.com/api' })
  apiSandboxUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Key features of the tool.',
    type: 'array', // Changed to array
    items: { type: 'string' }, // Assuming array of strings based on example
    example: ['Feature A', 'Feature B'],
  })
  keyFeatures?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Common use cases for the tool.',
    type: 'array', // Changed to array
    items: { type: 'string' }, // Assuming array of strings based on example
    example: ['Data analysis', 'Automation'],
  })
  useCases?: any | null; // Prisma Json type

  @ApiPropertyOptional({ enum: PricingModel, description: 'Pricing model of the tool.', example: PricingModel.FREEMIUM })
  pricingModel?: PricingModel | null;

  @ApiPropertyOptional({ enum: PriceRange, description: 'General price range of the tool.', example: PriceRange.MEDIUM })
  priceRange?: PriceRange | null;

  @ApiPropertyOptional({ description: 'Specific details about pricing.', example: 'Pro plan at $49/month.' })
  pricingDetails?: string | null;

  @ApiPropertyOptional({ description: 'URL to the pricing page.', example: 'https://example.com/pricing' })
  pricingUrl?: string | null;

  @ApiProperty({ description: 'Indicates if the tool has a free tier.', example: true, default: false })
  hasFreeTier: boolean;

  @ApiPropertyOptional({
    description: 'Supported platforms (e.g., Web, Windows, macOS, Linux).',
    type: 'array', // Changed to array
    items: { type: 'string' }, // Assuming array of strings based on example
    example: ['Web', 'Windows'],
  })
  platforms?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Known integrations with other tools or services.',
    type: 'array', // Changed to array
    items: { type: 'string' }, // Assuming array of strings based on example
    example: ['Slack', 'Google Drive'],
  })
  integrations?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Programming languages supported or relevant to the tool.',
    type: 'array', // Changed to array
    items: { type: 'string' }, // Assuming array of strings based on example
    example: ['Python', 'JavaScript'],
  })
  supportedLanguages?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Current version of the tool.', example: 'v2.5.1' })
  currentVersion?: string | null;

  @ApiPropertyOptional({ description: 'Date of the last version update.', example: '2023-10-15T00:00:00.000Z', type: String, format: 'date-time' })
  lastVersionUpdateDate?: Date | null;

  @ApiPropertyOptional({ description: 'URL to the tool\'s changelog.', example: 'https://example.com/changelog' })
  changelogUrl?: string | null;
} 