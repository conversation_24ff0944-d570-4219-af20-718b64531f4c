import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DatasetDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Format of the dataset (e.g., CSV, JSON, Parquet).', example: 'CSV' })
  format?: string | null;

  @ApiPropertyOptional({ description: 'URL to the source of the dataset.', example: 'https://example.com/dataset.csv' })
  sourceUrl?: string | null;

  @ApiPropertyOptional({ description: 'License under which the dataset is released (e.g., MIT, CC BY 4.0).', example: 'CC BY 4.0' })
  license?: string | null;

  @ApiPropertyOptional({ description: 'Size of the dataset in bytes.', example: 10485760, type: 'integer', format: 'int64' })
  sizeInBytes?: bigint | null;

  @ApiPropertyOptional({ description: 'A brief description of the dataset.', example: 'A collection of anonymized user interactions.' })
  description?: string | null;

  @ApiPropertyOptional({ description: 'Notes on how to access or use the dataset.', example: 'Requires authentication token for download.' })
  accessNotes?: string | null;

  @ApiProperty({ description: 'Timestamp of when the dataset details were created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the dataset details', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 