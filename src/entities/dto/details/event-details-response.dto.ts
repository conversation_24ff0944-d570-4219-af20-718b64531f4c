import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class EventDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'h1i2j3k4-l5m6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Type of the event (e.g., Conference, Webinar, Workshop, Meetup).', example: 'Conference' })
  eventType?: string | null;

  @ApiPropertyOptional({ description: 'Start date and time of the event.', example: '2024-09-15T09:00:00.000Z', type: String, format: 'date-time' })
  startDate?: Date | null;

  @ApiPropertyOptional({ description: 'End date and time of the event.', example: '2024-09-17T17:00:00.000Z', type: String, format: 'date-time' })
  endDate?: Date | null;

  @ApiPropertyOptional({ description: 'Location of the event (can be a physical address or \'Online\').', example: 'Online' })
  location?: string | null;

  @ApiPropertyOptional({ description: 'URL for event registration.', example: 'https://example.com/event/register' })
  registrationUrl?: string | null;

  @ApiPropertyOptional({
    description: 'List of speakers or key presenters at the event.',
    type: 'array',
    items: { type: 'string' }, // Or object if more details per speaker are needed
    example: ['Dr. AI Visionary', 'Prof. ML Innovator'],
  })
  speakerList?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'URL to the event agenda or schedule.', example: 'https://example.com/event/agenda' })
  agendaUrl?: string | null;

  @ApiPropertyOptional({ description: 'Pricing information for the event (e.g., Free, $99, Contact for enterprise).', example: '$99' })
  price?: string | null;

  @ApiProperty({ description: 'Timestamp of when the event details were created', example: '2023-08-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the event details', example: '2023-08-10T10:00:00.000Z' })
  updatedAt: Date;
} 