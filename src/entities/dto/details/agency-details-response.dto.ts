import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AgencyDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'e5f6g7h8-i9j0-1234-5678-901234abcdef' })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Services offered by the agency.',
    type: 'array',
    items: { type: 'string' }, // Assuming array of strings, adjust if structure is different
    example: ['AI Strategy Consulting', 'Custom Model Development', 'Data Annotation'],
  })
  servicesOffered?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Primary industries the agency focuses on.',
    type: 'array',
    items: { type: 'string' }, // Assuming array of strings
    example: ['Healthcare', 'Finance', 'E-commerce'],
  })
  industryFocus?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Typical size of clients the agency works with (e.g., startups, SMEs, enterprise).',
    type: 'array',
    items: { type: 'string' }, // Assuming array of strings
    example: ['SMEs', 'Enterprise'],
  })
  targetClientSize?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'Specific target audience or roles the agency serves.',
    type: 'array',
    items: { type: 'string' }, // Assuming array of strings
    example: ['CTOs', 'Product Managers'],
  })
  targetAudience?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Summary of the agency\'s location(s) or service areas.', example: 'Global, with offices in New York and London' })
  locationSummary?: string | null;

  @ApiPropertyOptional({ description: 'URL to the agency\'s portfolio or case studies.', example: 'https://example.agency/portfolio' })
  portfolioUrl?: string | null;

  @ApiPropertyOptional({ description: 'Information about the agency\'s pricing structure or engagement models.', example: 'Project-based, Retainer options available' })
  pricingInfo?: string | null;

  @ApiProperty({ description: 'Timestamp of when the agency details were created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the agency details', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 