import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class NewsDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'm1n2o3p4-q5r6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Publication date of the news article.', example: '2024-01-15', type: String, format: 'date' })
  publicationDate?: Date | null;

  @ApiPropertyOptional({ description: 'Name of the source or publisher of the news.', example: 'TechCrunch' })
  sourceName?: string | null;

  @ApiProperty({ description: 'URL to the original news article.', example: 'https://techcrunch.com/2024/01/15/ai-breakthrough/' })
  articleUrl: string;

  @ApiPropertyOptional({ description: 'Author(s) of the news article.', example: 'Jane Journalist' })
  author?: string | null;

  @ApiPropertyOptional({ description: 'A brief summary or abstract of the news article.', example: 'A new AI model has achieved state-of-the-art results in image generation...' })
  summary?: string | null;

  @ApiProperty({ description: 'Timestamp of when the news details were created', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the news details', example: '2024-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 