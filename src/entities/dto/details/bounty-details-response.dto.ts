import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BountyDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'k1l2m3n4-o5p6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Issuer of the bounty (e.g., company name, project name).', example: 'OpenSource Project X' })
  bountyIssuer?: string | null;

  @ApiPropertyOptional({ description: 'Reward amount for completing the bounty (e.g., 1000 USD, 0.5 ETH).', example: '500 USDC' })
  rewardAmount?: string | null;

  @ApiPropertyOptional({ description: 'Detailed requirements or tasks for the bounty.', example: 'Fix a critical bug in the authentication module. See issue #123.' })
  requirements?: string | null;

  @ApiPropertyOptional({ description: 'Submission deadline for the bounty.', example: '2024-11-30', type: String, format: 'date' })
  submissionDeadline?: Date | null;

  @ApiPropertyOptional({ description: 'URL to the platform where the bounty is listed or managed (e.g., Gitcoin, HackerOne).', example: 'https://gitcoin.co/bounties/123' })
  platformUrl?: string | null;

  @ApiPropertyOptional({ description: 'Difficulty level of the bounty (e.g., Easy, Medium, Hard).', example: 'Medium' })
  difficultyLevel?: string | null;

  @ApiProperty({ description: 'Timestamp of when the bounty details were created', example: '2023-11-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the bounty details', example: '2023-11-10T10:00:00.000Z' })
  updatedAt: Date;
} 