import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class NewsletterDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'h8i9j0k1-l2m3-4567-8901-234567abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Frequency of the newsletter publication (e.g., Daily, Weekly, Monthly).', example: 'Weekly' })
  frequency?: string | null;

  @ApiPropertyOptional({
    description: 'Main topics covered in the newsletter.',
    type: 'array',
    items: { type: 'string' },
    example: ['Latest AI Breakthroughs', 'Tool Reviews', 'Industry News'],
  })
  mainTopics?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'URL to the newsletter\'s archive.', example: 'https://example.com/newsletter/archive' })
  archiveUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL to subscribe to the newsletter.', example: 'https://example.com/newsletter/subscribe' })
  subscribeUrl?: string | null;

  @ApiPropertyOptional({ description: 'Name of the author or publisher.', example: 'AI Insights Team' })
  authorName?: string | null;

  @ApiPropertyOptional({ description: 'Number of subscribers.', example: 12000, type: Number })
  subscriberCount?: number | null;

  @ApiProperty({ description: 'Timestamp of when the newsletter details were created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the newsletter details', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 