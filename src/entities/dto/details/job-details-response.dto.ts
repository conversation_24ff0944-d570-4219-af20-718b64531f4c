import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class JobDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'i1j2k3l4-m5n6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Title of the job position.', example: 'AI Research Scientist' })
  jobTitle?: string | null;

  @ApiPropertyOptional({ description: 'Name of the company offering the job.', example: 'FutureTech AI Corp.' })
  companyName?: string | null;

  @ApiPropertyOptional({ description: 'Location type for the job (e.g., Remote, On-site, Hybrid).', example: 'Remote' })
  locationType?: string | null;

  @ApiPropertyOptional({ description: 'Salary range for the position (e.g., $120k - $150k, Competitive).', example: '$130,000 - $160,000 USD' })
  salaryRange?: string | null;

  @ApiPropertyOptional({ description: 'URL to the job application page.', example: 'https://futuretech.ai/careers/apply?jobId=123' })
  applicationUrl?: string | null;

  @ApiPropertyOptional({ description: 'Full description of the job role, responsibilities, and qualifications.', example: 'Seeking an experienced AI Research Scientist to lead innovative projects...' })
  jobDescription?: string | null;

  @ApiPropertyOptional({ description: 'Required or preferred experience level (e.g., Entry, Mid, Senior).', example: 'Senior (5+ years)' })
  experienceLevel?: string | null;

  @ApiPropertyOptional({ description: 'Type of employment (e.g., Full-time, Part-time, Contract).', example: 'Full-time' })
  employmentType?: string | null;

  @ApiProperty({ description: 'Timestamp of when the job details were created', example: '2023-09-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the job details', example: '2023-09-10T10:00:00.000Z' })
  updatedAt: Date;
} 