import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BookDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'n1o2p3q4-r5s6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Author(s) of the book.',
    type: 'array',
    items: { type: 'string' },
    example: ['Dr. AI Scholar', 'Prof. Data Insights'],
  })
  authorNames?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'ISBN of the book.', example: '978-3-16-148410-0' })
  isbn?: string | null;

  @ApiPropertyOptional({ description: 'Publisher of the book.', example: 'AI Press International' })
  publisher?: string | null;

  @ApiPropertyOptional({ description: 'Year the book was published.', example: 2023, type: 'integer' })
  publicationYear?: number | null;

  @ApiPropertyOptional({ description: 'Number of pages in the book.', example: 350, type: 'integer' })
  pageCount?: number | null;

  @ApiPropertyOptional({ description: 'A brief summary or abstract of the book.', example: 'A comprehensive guide to modern AI techniques and applications...' })
  summary?: string | null;

  @ApiPropertyOptional({ description: 'URL to purchase or access the book.', example: 'https://example.com/books/ai-guide' })
  purchaseUrl?: string | null;

  @ApiProperty({ description: 'Timestamp of when the book details were created', example: '2024-02-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the book details', example: '2024-02-10T10:00:00.000Z' })
  updatedAt: Date;
} 