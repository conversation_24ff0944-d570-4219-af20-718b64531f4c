import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ContentCreatorDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'f6g7h8i9-j0k1-2345-6789-012345abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Name of the content creator or channel.', example: 'AI Explained Today' })
  creatorName?: string | null;

  @ApiPropertyOptional({ description: 'Primary platform where the content is published (e.g., YouTube, Twitch, Blog).', example: 'YouTube' })
  primaryPlatform?: string | null;

  @ApiPropertyOptional({
    description: 'Main focus areas or topics covered by the content creator.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI News', 'Machine Learning Tutorials', 'Tech Reviews'],
  })
  focusAreas?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Number of followers or subscribers.', example: 150000, type: Number })
  followerCount?: number | null;

  @ApiPropertyOptional({ description: 'URL to an example piece of content or the main channel/profile.', example: 'https://youtube.com/c/AIExplainedToday' })
  exampleContentUrl?: string | null;

  @ApiProperty({ description: 'Timestamp of when the content creator details were created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the content creator details', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 