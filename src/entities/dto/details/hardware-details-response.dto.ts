import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class HardwareDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'l1m2n3o4-p5q6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Type of hardware (e.g., GPU, FPGA, ASIC, TPU, AI Accelerator).', example: 'GPU' })
  hardwareType?: string | null;

  @ApiPropertyOptional({
    description: 'Key specifications of the hardware.',
    type: 'object',
    additionalProperties: true, // Allows for flexible key-value pairs
    example: { memory: '24GB GDDR6X', cuda_cores: 10496, tflops: 35.6, power_consumption: '350W' },
  })
  specifications?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Manufacturer of the hardware.', example: 'NVIDIA' })
  manufacturer?: string | null;

  @ApiPropertyOptional({ description: 'Release date of the hardware.', example: '2023-03-15', type: String, format: 'date' })
  releaseDate?: Date | null;

  @ApiPropertyOptional({ description: 'Typical price range for the hardware.', example: '$699 - $999' })
  priceRange?: string | null;

  @ApiPropertyOptional({ description: 'URL to the hardware datasheet or product page.', example: 'https://nvidia.com/products/gpu/rtx4080' })
  datasheetUrl?: string | null;

  @ApiProperty({ description: 'Timestamp of when the hardware details were created', example: '2023-12-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the hardware details', example: '2023-12-10T10:00:00.000Z' })
  updatedAt: Date;
} 