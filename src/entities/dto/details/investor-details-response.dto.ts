import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InvestorDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'g1h2i3j4-k5l6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Areas of focus for investment.',
    type: 'array',
    items: { type: 'string' },
    example: ['Seed Stage AI', 'Healthcare Tech', 'B2B SaaS'],
  })
  investmentFocusAreas?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: "URL to the investor or firm's portfolio.", example: 'https://investor.com/portfolio' })
  portfolioUrl?: string | null;

  @ApiPropertyOptional({ description: 'Typical size of investment (e.g., $100k - $1M).', example: '$250k - $750k' })
  typicalInvestmentSize?: string | null;

  @ApiPropertyOptional({
    description: 'Investment stages the investor participates in.',
    type: 'array',
    items: { type: 'string' },
    example: ['Pre-seed', 'Seed', 'Series A'],
  })
  investmentStages?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Contact email for inquiries or pitches.', example: '<EMAIL>' })
  contactEmail?: string | null;

  @ApiPropertyOptional({ description: 'Preferred method of communication for initial contact.', example: 'Email introduction via mutual connection' })
  preferredCommunication?: string | null;

  @ApiProperty({ description: 'Timestamp of when the investor details were created', example: '2023-07-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the investor details', example: '2023-07-10T10:00:00.000Z' })
  updatedAt: Date;
} 