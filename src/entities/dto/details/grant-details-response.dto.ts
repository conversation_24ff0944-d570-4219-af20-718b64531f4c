import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GrantDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'j1k2l3m4-n5o6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Name of the institution or organization providing the grant.', example: 'AI Research Foundation' })
  grantingInstitution?: string | null;

  @ApiPropertyOptional({ description: 'Eligibility criteria for the grant.', example: 'PhD students in AI ethics, Postdoctoral researchers in NLP.' })
  eligibilityCriteria?: string | null;

  @ApiPropertyOptional({ description: 'Application deadline for the grant.', example: '2024-12-31', type: String, format: 'date' })
  applicationDeadline?: Date | null;

  @ApiPropertyOptional({ description: 'Funding amount or range for the grant (e.g., $10,000 - $50,000).', example: 'Up to $25,000' })
  fundingAmount?: string | null;

  @ApiPropertyOptional({ description: 'URL to the grant application page or information page.', example: 'https://airesearchfoundation.org/grants/apply' })
  applicationUrl?: string | null;

  @ApiPropertyOptional({ description: 'Specific focus area of the grant.', example: 'AI for Social Good' })
  grantFocusArea?: string | null;

  @ApiProperty({ description: 'Timestamp of when the grant details were created', example: '2023-10-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the grant details', example: '2023-10-10T10:00:00.000Z' })
  updatedAt: Date;
} 