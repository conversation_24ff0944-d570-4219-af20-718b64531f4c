import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SkillLevel } from '../../../../generated/prisma'; // Adjust path as needed

export class CourseDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'i9j0k1l2-m3n4-5678-9012-345678abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Name of the instructor or course provider.', example: 'Dr. AI Expert' })
  instructorName?: string | null;

  @ApiPropertyOptional({ description: 'Estimated duration to complete the course.', example: 'Approx. 40 hours' })
  durationText?: string | null;

  @ApiPropertyOptional({ enum: SkillLevel, description: 'Recommended skill level for the course.', example: SkillLevel.BEGINNER })
  skillLevel?: SkillLevel | null;

  @ApiPropertyOptional({ description: 'Any prerequisites for taking the course.', example: 'Basic understanding of Python.' })
  prerequisites?: string | null;

  @ApiPropertyOptional({ description: 'URL to the course syllabus or detailed outline.', example: 'https://example.com/course/syllabus' })
  syllabusUrl?: string | null;

  @ApiPropertyOptional({ description: 'Number of students enrolled in the course.', example: 2500, type: Number })
  enrollmentCount?: number | null;

  @ApiPropertyOptional({ description: 'Indicates if a certificate is available upon completion.', example: true, default: false })
  certificateAvailable?: boolean | null;
} 