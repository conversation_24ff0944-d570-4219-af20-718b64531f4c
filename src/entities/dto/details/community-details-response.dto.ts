import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CommunityDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'g7h8i9j0-k1l2-3456-7890-123456abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Platform where the community is hosted (e.g., Discord, Slack, Discourse).', example: 'Discord' })
  platform?: string | null;

  @ApiPropertyOptional({ description: 'Number of members in the community.', example: 5000, type: Number })
  memberCount?: number | null;

  @ApiPropertyOptional({
    description: 'Main topics or focus areas of the community.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI Safety', 'LLM Development', 'Open Source AI'],
  })
  focusTopics?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'URL to the community rules or guidelines.', example: 'https://example.com/community/rules' })
  rulesUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL to join or get an invite to the community.', example: 'https://discord.gg/communityXyz' })
  inviteUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL to the main channel or landing page of the community.', example: 'https://example.com/community/main' })
  mainChannelUrl?: string | null;

  @ApiProperty({ description: 'Timestamp of when the community details were created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the community details', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 