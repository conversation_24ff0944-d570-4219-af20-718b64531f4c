import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PodcastDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'o1p2q3r4-s5t6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Host(s) of the podcast.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI Enthusiast A', 'Tech Podcaster B'],
  })
  hostNames?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Average length of a podcast episode.', example: '45 minutes' })
  averageEpisodeLength?: string | null;

  @ApiPropertyOptional({
    description: 'Main topics covered by the podcast.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI Ethics', 'Machine Learning Research', 'Future of Technology'],
  })
  mainTopics?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'URL to listen to the podcast (e.g., Spotify, Apple Podcasts).', example: 'https://open.spotify.com/show/podcast123' })
  listenUrl?: string | null;

  @ApiPropertyOptional({ description: 'Frequency of new podcast episodes (e.g., Weekly, Bi-weekly).', example: 'Weekly' })
  frequency?: string | null;

  @ApiPropertyOptional({ description: 'Primary language of the podcast.', example: 'English', default: 'English' })
  primaryLanguage?: string | null;

  @ApiProperty({ description: 'Timestamp of when the podcast details were created', example: '2024-03-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the podcast details', example: '2024-03-10T10:00:00.000Z' })
  updatedAt: Date;
} 