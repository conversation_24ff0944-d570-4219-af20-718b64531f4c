import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ServiceProviderDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'f1g2h3i4-j5k6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Areas of service provided.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI Development', 'Data Science Consulting', 'MLOps Implementation'],
  })
  serviceAreas?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'URL to case studies or portfolio.', example: 'https://provider.com/case-studies' })
  caseStudiesUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL to book a consultation.', example: 'https://provider.com/book-consultation' })
  consultationBookingUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Industries the service provider specializes in.',
    type: 'array',
    items: { type: 'string' },
    example: ['Healthcare', 'Finance', 'Retail'],
  })
  industrySpecializations?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Typical size of companies the provider works with (e.g., Startups, SMEs, Enterprise).', example: 'SMEs' })
  companySizeFocus?: string | null;

  @ApiPropertyOptional({ description: 'Typical hourly rate range.', example: '$150 - $250' })
  hourlyRateRange?: string | null;

  @ApiProperty({ description: 'Timestamp of when the service provider details were created', example: '2023-06-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the service provider details', example: '2023-06-10T10:00:00.000Z' })
  updatedAt: Date;
} 