import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PlatformDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'p1q2r3s4-t5u6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Type of platform (e.g., PaaS, SaaS, IaaS, MLOps Platform, Data Platform).', example: 'MLOps Platform' })
  platformType?: string | null;

  @ApiPropertyOptional({
    description: 'Key services offered by the platform.',
    type: 'array',
    items: { type: 'string' },
    example: ['Model Training & Experimentation', 'Automated Deployment Pipelines', 'Data Versioning & Lineage', 'Monitoring & Observability'],
  })
  keyServices?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: "URL to the platform's main documentation.", example: 'https://platform.example.com/docs' })
  documentationUrl?: string | null;

  @ApiPropertyOptional({ description: 'Pricing model of the platform (e.g., Subscription, Usage-based, Free Tier Available).', example: 'Usage-based with enterprise tiers' })
  pricingModel?: string | null;

  @ApiPropertyOptional({ description: 'URL to the Service Level Agreement (SLA) documentation.', example: 'https://platform.example.com/sla' })
  slaUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Geographical regions where the platform services are available.',
    type: 'array',
    items: { type: 'string' },
    example: ['us-east-1', 'eu-west-2', 'ap-southeast-1'],
  })
  supportedRegions?: any | null; // Prisma Json type

  @ApiProperty({ description: 'Timestamp of when the platform details were created', example: '2024-04-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the platform details', example: '2024-04-10T10:00:00.000Z' })
  updatedAt: Date;
} 