import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProjectReferenceDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'e1f2g3h4-i5j6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Current status of the project (e.g., active, completed, archived, proof-of-concept).', example: 'active' })
  projectStatus?: string | null;

  @ApiPropertyOptional({ description: 'URL to the source code repository.', example: 'https://github.com/user/project' })
  sourceCodeUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL to a live demo of the project.', example: 'https://project-demo.example.com' })
  liveDemoUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Technologies used in the project.',
    type: 'array',
    items: { type: 'string' },
    example: ['React', 'Node.js', 'PostgreSQL', 'Docker'],
  })
  technologies?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'Main goals or objectives of the project.', example: 'To demonstrate real-time data visualization.' })
  projectGoals?: string | null;

  @ApiPropertyOptional({
    description: 'List of contributors or team members.',
    type: 'array',
    items: { type: 'string' }, // Can also be object if more details needed per contributor
    example: ['Jane Doe', 'John Smith'],
  })
  contributors?: any | null; // Prisma Json type

  @ApiProperty({ description: 'Timestamp of when the project reference details were created', example: '2023-05-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the project reference details', example: '2023-05-10T10:00:00.000Z' })
  updatedAt: Date;
} 