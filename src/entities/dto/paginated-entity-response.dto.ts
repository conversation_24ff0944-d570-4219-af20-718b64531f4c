import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EntityResponseDto } from './entity-response.dto';

export class PaginatedEntityResponseDto {
  @ApiProperty({
    description: 'Array of entity objects for the current page.',
    type: [EntityResponseDto],
  })
  data: EntityResponseDto[];

  @ApiProperty({ description: 'Total number of entities matching the query.', example: 150 })
  total: number;

  @ApiProperty({ description: 'Current page number.', example: 1 })
  page: number;

  @ApiProperty({ description: 'Number of items per page.', example: 10 })
  limit: number;

  @ApiProperty({ description: 'Total number of pages.', example: 15 })
  totalPages: number;

  @ApiPropertyOptional({ description: 'Search query used, if any.', example: 'AI tools' })
  search?: string | null;

  // TODO: Consider adding filter and sort information here if needed in the future
} 