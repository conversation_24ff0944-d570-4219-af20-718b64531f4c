import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EntityTypeResponseDto } from '../../entity-types/dto/entity-type-response.dto';
import { UserProfileMinimalDto } from '../../auth/dto/user-profile-minimal.dto';
import { CategoryResponseDto } from '../../categories/dto/category-response.dto';
import { TagResponseDto } from '../../tags/dto/tag-response.dto';
import { EntityStatus } from '../../../generated/prisma';
import { ReviewResponseDto } from '../../reviews/dto/review-response.dto';
import { FeatureResponseDto } from '../../features/dto/feature-response.dto';

// Import all specific detail DTOs
import { ToolDetailsResponseDto } from './details/tool-details-response.dto';
import { CourseDetailsResponseDto } from './details/course-details-response.dto';
import { DatasetDetailsResponseDto } from './details/dataset-details-response.dto';
import { ResearchPaperDetailsResponseDto } from './details/research-paper-details-response.dto';
import { SoftwareDetailsResponseDto } from './details/software-details-response.dto';
import { ModelDetailsResponseDto } from './details/model-details-response.dto';
import { ProjectReferenceDetailsResponseDto } from './details/project-reference-details-response.dto';
import { ServiceProviderDetailsResponseDto } from './details/service-provider-details-response.dto';
import { InvestorDetailsResponseDto } from './details/investor-details-response.dto';
import { CommunityDetailsResponseDto } from './details/community-details-response.dto';
import { EventDetailsResponseDto } from './details/event-details-response.dto';
import { JobDetailsResponseDto } from './details/job-details-response.dto';
import { GrantDetailsResponseDto } from './details/grant-details-response.dto';
import { BountyDetailsResponseDto } from './details/bounty-details-response.dto';
import { HardwareDetailsResponseDto } from './details/hardware-details-response.dto';
import { NewsDetailsResponseDto } from './details/news-details-response.dto';
import { BookDetailsResponseDto } from './details/book-details-response.dto';
import { PodcastDetailsResponseDto } from './details/podcast-details-response.dto';
import { NewsletterDetailsResponseDto } from './details/newsletter-details-response.dto';
import { PlatformDetailsResponseDto } from './details/platform-details-response.dto';
import { AgencyDetailsResponseDto } from './details/agency-details-response.dto';
import { ContentCreatorDetailsResponseDto } from './details/content-creator-details-response.dto';

// Union type for Swagger (more complex setup, for now using 'any' as per instructions)
// type AllDetailsDto =
//   | ToolDetailsResponseDto
//   | CourseDetailsResponseDto
//   // ... include all other DetailsResponseDto types

export class EntityResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID)', example: '123e4567-e89b-12d3-a456-************' })
  id: string;

  @ApiProperty({ description: 'Name of the entity', example: 'Super AI Tool' })
  name: string;

  @ApiPropertyOptional({ description: 'Website URL of the entity', example: 'https://superaitool.com' })
  websiteUrl?: string | null;

  @ApiPropertyOptional({ description: 'Type of the entity', type: () => EntityTypeResponseDto })
  entityType?: EntityTypeResponseDto | null;

  @ApiPropertyOptional({ description: 'Short description of the entity', example: 'A tool that revolutionizes AI development.' })
  shortDescription?: string | null;

  @ApiPropertyOptional({ description: 'Full description of the entity', example: 'Super AI Tool offers a comprehensive suite of features...' })
  description?: string | null;

  @ApiPropertyOptional({ description: "URL of the entity's logo", example: 'https://superaitool.com/logo.png' })
  logoUrl?: string | null;

  @ApiPropertyOptional({ description: "URL to the entity's documentation", example: 'https://docs.superaitool.com' })
  documentationUrl?: string | null;

  @ApiPropertyOptional({ description: 'URL for contacting the entity', example: 'https://superaitool.com/contact' })
  contactUrl?: string | null;

  @ApiPropertyOptional({ description: "URL to the entity's privacy policy", example: 'https://superaitool.com/privacy' })
  privacyPolicyUrl?: string | null;

  @ApiPropertyOptional({ description: 'Year the entity was founded', example: 2022, type: 'integer' })
  foundedYear?: number | null;

  @ApiProperty({ enum: EntityStatus, description: 'Current status of the entity', example: EntityStatus.ACTIVE })
  status: EntityStatus;

  @ApiPropertyOptional({
    description: 'Social media links for the entity',
    type: 'object',
    additionalProperties: { type: 'string' },
    example: { twitter: 'https://twitter.com/superaitool', linkedIn: 'https://linkedin.com/company/superaitool' },
  })
  socialLinks?: any | null; // Prisma Json type

  @ApiPropertyOptional({ description: 'User who submitted the entity', type: () => UserProfileMinimalDto })
  submitter?: UserProfileMinimalDto | null;

  @ApiPropertyOptional({ description: 'Legacy ID if applicable', example: 'old-system-id-123' })
  legacyId?: string | null;

  @ApiProperty({ description: 'Number of reviews for the entity', example: 42, default: 0, type: 'integer' })
  reviewCount: number;

  @ApiProperty({ description: 'Average rating for the entity', example: 4.5, default: 0, type: 'number', format: 'float' })
  avgRating: number;

  @ApiProperty({ description: 'Timestamp of when the entity was created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the entity', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Type-specific details of the entity. The structure depends on the entityType.',
    type: 'object',
    additionalProperties: true,
    example: { keyFeatures: ['Feature A', 'Feature B'], hasApi: true }, // Example for a tool
  })
  details?: any; // This will be populated with the specific DetailsResponseDto

  @ApiProperty({ description: 'Categories associated with the entity', type: () => [CategoryResponseDto] })
  categories: CategoryResponseDto[];

  @ApiProperty({ description: 'Tags associated with the entity', type: () => [TagResponseDto] })
  tags: TagResponseDto[];

  @ApiPropertyOptional({ type: () => [FeatureResponseDto] })
  features?: FeatureResponseDto[];

  @ApiPropertyOptional({ type: () => [ReviewResponseDto] })
  reviews?: ReviewResponseDto[];
} 