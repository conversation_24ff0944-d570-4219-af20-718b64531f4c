import {
  Injectable,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  ForbiddenException,
  BadRequestException,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { ListEntitiesDto } from './dto/list-entities.dto';
import {
  Entity,
  Prisma,
  User as UserModel,
  EntityStatus,
  EntityType,
  UserRole,
  ReviewStatus,
  EntityDetailsAgency,
  EntityDetailsBook,
  EntityDetailsBounty,
  EntityDetailsCommunity,
  EntityDetailsContentCreator,
  EntityDetailsCourse,
  EntityDetailsDataset,
  EntityDetailsEvent,
  EntityDetailsGrant,
  EntityDetailsHardware,
  EntityDetailsInvestor,
  EntityDetailsJob,
  EntityDetailsModel,
  EntityDetailsNews,
  EntityDetailsNewsletter,
  EntityDetailsPlatform,
  EntityDetailsPodcast,
  EntityDetailsProjectReference,
  EntityDetailsResearchPaper,
  EntityDetailsServiceProvider,
  EntityDetailsSoftware,
  EntityDetailsTool,
  AffiliateStatus,
  PricingModel,
  PriceRange,
  SkillLevel,
  LearningCurve,
  TechnicalLevel,
} from 'generated/prisma';
import { PaginatedResponse } from '../common/interfaces/paginated-response.interface';
import {
  mapCategoriesToConnectOrCreate,
  mapTagsToConnectOrCreate,
} from '../utils/prisma-helpers';

// Helper function to map entity type slugs (from DB) to their corresponding detail DTO keys
const entityTypeSlugToDetailKey = (
  slug: string,
): keyof UpdateEntityDto | null => {
  const mapping: Record<string, keyof UpdateEntityDto> = {
    'ai-tool': 'tool_details',
    'online-course': 'course_details',
    'agency': 'agency_details',
    'content-creator': 'content_creator_details',
    'community': 'community_details',
    'newsletter': 'newsletter_details',
    'dataset': 'dataset_details',
    'research-paper': 'research_paper_details',
    'software': 'software_details',
    'model': 'model_details',
    'project-reference': 'project_reference_details',
    'service-provider': 'service_provider_details',
    'investor': 'investor_details',
    'event': 'event_details',
    'job': 'job_details',
    'grant': 'grant_details',
    'bounty': 'bounty_details',
    'hardware': 'hardware_details',
    'news': 'news_details',
    'book': 'book_details',
    'podcast': 'podcast_details',
    'platform': 'platform_details',
  };
  return mapping[slug] || null;
};

// Helper function to map entity type slugs (from DB) to their corresponding Prisma relation keys
const entityTypeSlugToPrismaDetailKey = (
  slug: string,
): keyof Prisma.EntityInclude | null => {
  const mapping: Record<string, keyof Prisma.EntityInclude> = {
    'ai-tool': 'entityDetailsTool',
    'online-course': 'entityDetailsCourse',
    'agency': 'entityDetailsAgency',
    'content-creator': 'entityDetailsContentCreator',
    'community': 'entityDetailsCommunity',
    'newsletter': 'entityDetailsNewsletter',
    'dataset': 'entityDetailsDataset',
    'research-paper': 'entityDetailsResearchPaper',
    'software': 'entityDetailsSoftware',
    'model': 'entityDetailsModel',
    'project-reference': 'entityDetailsProjectReference',
    'service-provider': 'entityDetailsServiceProvider',
    'investor': 'entityDetailsInvestor',
    'event': 'entityDetailsEvent',
    'job': 'entityDetailsJob',
    'grant': 'entityDetailsGrant',
    'bounty': 'entityDetailsBounty',
    'hardware': 'entityDetailsHardware',
    'news': 'entityDetailsNews',
    'book': 'entityDetailsBook',
    'podcast': 'entityDetailsPodcast',
    'platform': 'entityDetailsPlatform',
  };
  return mapping[slug] || null;
};

@Injectable()
export class EntitiesService implements OnModuleInit {
  private entityTypeMap: Map<string, string> = new Map();
  private readonly logger = new Logger(EntitiesService.name);

  constructor(
    private readonly prisma: PrismaService,
  ) {
    console.log('----------------------------------------------------');
    console.log('[EntitiesService] CONSTRUCTOR CALLED');
    console.log('----------------------------------------------------');
  }

  async onModuleInit() {
    console.log('----------------------------------------------------');
    console.log('[EntitiesService] ON_MODULE_INIT CALLED. Attempting to load entity types...');
    console.log('----------------------------------------------------');
    await this.loadEntityTypes();
  }

  private async loadEntityTypes() {
    console.log('[EntitiesService] loadEntityTypes - STARTING');
    try {
      const entityTypes = await this.prisma.entityType.findMany({
        select: { id: true, slug: true },
      });
      if (entityTypes && entityTypes.length > 0) {
        this.entityTypeMap.clear();
        entityTypes.forEach(type => this.entityTypeMap.set(type.id, type.slug));
        console.log(`[EntitiesService] Entity types loaded into map. Count: ${this.entityTypeMap.size}.`);
        if (this.entityTypeMap.size > 0) {
            const firstKey = this.entityTypeMap.keys().next().value;
            if (firstKey) {
                 console.log(`[EntitiesService] Sample map entry - ID: ${firstKey}, Slug: ${this.entityTypeMap.get(firstKey)}`);
            }
        }
        console.log('[EntitiesService] loadEntityTypes - FINISHED SUCCESSFULLY');
      } else {
        console.log('[EntitiesService] No entity types found in the database to load into map.');
      }
    } catch (error) {
      console.error('[EntitiesService] loadEntityTypes - CRITICAL FAILURE:', error);
      this.logger.error('[EntitiesService] CRITICAL: Failed to load entity types on startup:', error.stack);
    }
  }

  // Helper to map shared DTO fields to Prisma input for details objects
  private mapSharedDetailsToPrisma(detailsDto: any, entityTypeSlug?: string) {
    if (!detailsDto) return {};
    const prismaData: any = {};

    if (detailsDto.has_free_tier !== undefined) prismaData.hasFreeTier = detailsDto.has_free_tier;
    if (detailsDto.use_cases !== undefined) prismaData.useCases = detailsDto.use_cases as Prisma.JsonArray;
    if (detailsDto.integrations !== undefined) prismaData.integrations = detailsDto.integrations as Prisma.JsonArray;
    if (detailsDto.key_features !== undefined) prismaData.keyFeatures = detailsDto.key_features as Prisma.JsonArray;

    if (detailsDto.pricing_model !== undefined) prismaData.pricingModel = detailsDto.pricing_model;
    if (detailsDto.price_range !== undefined) prismaData.priceRange = detailsDto.price_range;
    if (detailsDto.pricing_details !== undefined) prismaData.pricingDetails = detailsDto.pricing_details;
    if (detailsDto.pricing_url !== undefined) prismaData.pricingUrl = detailsDto.pricing_url;
    if (detailsDto.support_email !== undefined) prismaData.supportEmail = detailsDto.support_email;
    if (detailsDto.has_live_chat !== undefined) prismaData.hasLiveChat = detailsDto.has_live_chat;
    if (detailsDto.community_url !== undefined) prismaData.communityUrl = detailsDto.community_url;

    if (detailsDto.learning_curve !== undefined) prismaData.learningCurve = detailsDto.learning_curve;
    if (detailsDto.technical_level !== undefined) prismaData.technicalLevel = detailsDto.technical_level;
    
    if (detailsDto.status !== undefined) {
       prismaData.status = detailsDto.status;
    }
    return prismaData;
  }

  async create(createEntityDto: CreateEntityDto, submitterUser: UserModel): Promise<Entity> {
    const {
      entity_type_id,
      category_ids,
      tag_ids,
      feature_ids,

      // Destructure all direct entity fields from DTO
      name, 
      website_url,
      short_description,
      description,
      logo_url,
      documentation_url,
      contact_url,
      privacy_policy_url,
      founded_year,
      social_links,
      meta_title, // New
      meta_description, // New
      employee_count_range, // New
      funding_stage, // New
      location_summary, // New
      ref_link, // New
      affiliate_status, // New
      scraped_review_sentiment_label, // New
      scraped_review_sentiment_score, // New
      scraped_review_count, // New
      
      // Destructure all detail DTOs
      tool_details,
      course_details,
      agency_details,
      content_creator_details,
      community_details,
      newsletter_details,
      dataset_details, // New
      research_paper_details, // New
      software_details, // New
      model_details, // New
      project_reference_details, // New
      service_provider_details, // New
      investor_details, // New
      event_details, // New
      job_details, // New
      grant_details, // New
      bounty_details, // New
      hardware_details, // New
      news_details, // New
      book_details, // New
      podcast_details, // New
      platform_details, // New
    } = createEntityDto;

    this.logger.log(`[EntitiesService Create] Received entity_type_id: ${entity_type_id}`);
    this.logger.log(`[EntitiesService Create] Current entityTypeMap (size ${this.entityTypeMap.size}): ${JSON.stringify(Array.from(this.entityTypeMap.entries()))}`);

    const entityTypeSlug = this.entityTypeMap.get(entity_type_id);

    if (!entityTypeSlug) {
      this.logger.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys()).join(', ')}]`);
      console.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys())}`);
      throw new BadRequestException(`Invalid entity_type_id: ${entity_type_id}`);
    }
    
    const providedDetails: { [key: string]: any } = {
        'ai-tool': tool_details,
        'online-course': course_details,
        'agency': agency_details,
        'content-creator': content_creator_details,
        'community': community_details,
        'newsletter': newsletter_details,
        'dataset': dataset_details, // New
        'research-paper': research_paper_details, // New
        'software': software_details, // New
        'model': model_details, // New
        'project-reference': project_reference_details, // New
        'service-provider': service_provider_details, // New
        'investor': investor_details, // New
        'event': event_details, // New
        'job': job_details, // New
        'grant': grant_details, // New
        'bounty': bounty_details, // New
        'hardware': hardware_details, // New
        'news': news_details, // New
        'book': book_details, // New
        'podcast': podcast_details, // New
        'platform': platform_details, // New
    };

    let expectedDetailKeyForSlug = entityTypeSlugToDetailKey(entityTypeSlug);

    // Refined logic from user:
    let detailIsProvided = false;
    if (tool_details) detailIsProvided = true;
    if (course_details) detailIsProvided = true;
    if (agency_details) detailIsProvided = true;
    if (content_creator_details) detailIsProvided = true;
    if (community_details) detailIsProvided = true;
    if (newsletter_details) detailIsProvided = true;
    if (dataset_details) detailIsProvided = true; // New
    if (research_paper_details) detailIsProvided = true; // New
    if (software_details) detailIsProvided = true; // New
    if (model_details) detailIsProvided = true; // New
    if (project_reference_details) detailIsProvided = true; // New
    if (service_provider_details) detailIsProvided = true; // New
    if (investor_details) detailIsProvided = true; // New
    if (event_details) detailIsProvided = true; // New
    if (job_details) detailIsProvided = true; // New
    if (grant_details) detailIsProvided = true; // New
    if (bounty_details) detailIsProvided = true; // New
    if (hardware_details) detailIsProvided = true; // New
    if (news_details) detailIsProvided = true; // New
    if (book_details) detailIsProvided = true; // New
    if (podcast_details) detailIsProvided = true; // New
    if (platform_details) detailIsProvided = true; // New

    let correctDetailIsPresent = false;
    if (entityTypeSlug === 'ai-tool' && tool_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'online-course' && course_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'agency' && agency_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'content-creator' && content_creator_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'community' && community_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'newsletter' && newsletter_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'dataset' && dataset_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'research-paper' && research_paper_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'software' && software_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'model' && model_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'project-reference' && project_reference_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'service-provider' && service_provider_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'investor' && investor_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'event' && event_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'job' && job_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'grant' && grant_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'bounty' && bounty_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'hardware' && hardware_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'news' && news_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'book' && book_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'podcast' && podcast_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'platform' && platform_details) correctDetailIsPresent = true; // New

    if (detailIsProvided && !correctDetailIsPresent) {
       throw new BadRequestException(
         `Incorrect detail DTO provided for entity type '${entityTypeSlug}'. Expected '${expectedDetailKeyForSlug || entityTypeSlug + '_details'}'.`
       );
    }

    const actualProvidedDetailObjects = [
        tool_details, course_details, agency_details, 
        content_creator_details, community_details, newsletter_details,
        dataset_details, research_paper_details, software_details, model_details,
        project_reference_details, service_provider_details, investor_details,
        event_details, job_details, grant_details, bounty_details, hardware_details,
        news_details, book_details, podcast_details, platform_details,
    ].filter(detail => detail !== undefined);

    if (actualProvidedDetailObjects.length > 1) {
        throw new BadRequestException('Only one type of entity details can be provided alongside the main entity data.');
    }

    // Consolidate all direct entity fields for Prisma
    const directEntityData: Omit<Prisma.EntityCreateInput, 'entityType' | 'submitter' | 'status' | 'entityCategories' | 'entityTags' | 'entityDetailsTool' | 'entityDetailsCourse' | 'entityDetailsAgency' | 'entityDetailsContentCreator' | 'entityDetailsCommunity' | 'entityDetailsNewsletter' | 'entityDetailsDataset' | 'entityDetailsResearchPaper' | 'entityDetailsSoftware' | 'entityDetailsModel' | 'entityDetailsProjectReference' | 'entityDetailsServiceProvider' | 'entityDetailsInvestor' | 'entityDetailsEvent' | 'entityDetailsGrant' | 'entityDetailsBounty' | 'entityDetailsHardware' | 'entityDetailsNews' | 'entityDetailsBook' | 'entityDetailsPodcast' | 'entityDetailsPlatform'> = {
        name,
        websiteUrl: website_url,
        shortDescription: short_description,
        description,
        logoUrl: logo_url,
        documentationUrl: documentation_url,
        contactUrl: contact_url,
        privacyPolicyUrl: privacy_policy_url,
        foundedYear: founded_year,
        socialLinks: social_links as Prisma.InputJsonValue | undefined,
        metaTitle: meta_title, // New
        metaDescription: meta_description, // New
        employeeCountRange: employee_count_range, // New
        fundingStage: funding_stage, // New
        locationSummary: location_summary, // New
        refLink: ref_link, // New
        affiliateStatus: affiliate_status, // New
        scrapedReviewSentimentLabel: scraped_review_sentiment_label, // New
        scrapedReviewSentimentScore: scraped_review_sentiment_score, // New
        scrapedReviewCount: scraped_review_count, // New
        // vectorEmbedding will be handled by a trigger or separate process, not directly in create DTO
    };

    return this.prisma.$transaction(async (tx) => {
        const createData: Prisma.EntityCreateInput = {
            ...directEntityData, // Spread the direct data
            entityType: { connect: { id: entity_type_id } },
            submitter: { connect: { id: submitterUser.id } },
            status: EntityStatus.PENDING, // Default status
            
            // Dynamic details based on entityTypeSlug
            ...(entityTypeSlug === 'ai-tool' && tool_details && { entityDetailsTool: { create: tool_details } }),
            ...(entityTypeSlug === 'online-course' && course_details && { entityDetailsCourse: { create: course_details } }),
            ...(entityTypeSlug === 'agency' && agency_details && { entityDetailsAgency: { create: agency_details } }),
            ...(entityTypeSlug === 'content-creator' && content_creator_details && { entityDetailsContentCreator: { create: content_creator_details } }),
            ...(entityTypeSlug === 'community' && community_details && { entityDetailsCommunity: { create: community_details } }),
            ...(entityTypeSlug === 'newsletter' && newsletter_details && { entityDetailsNewsletter: { create: newsletter_details } }),
            ...(entityTypeSlug === 'dataset' && dataset_details && { entityDetailsDataset: { create: dataset_details } }), // New
            ...(entityTypeSlug === 'research-paper' && research_paper_details && { entityDetailsResearchPaper: { create: research_paper_details } }), // New
            ...(entityTypeSlug === 'software' && software_details && { entityDetailsSoftware: { create: software_details } }), // New
            ...(entityTypeSlug === 'model' && model_details && { entityDetailsModel: { create: model_details } }), // New
            ...(entityTypeSlug === 'project-reference' && project_reference_details && { entityDetailsProjectReference: { create: project_reference_details } }), // New
            ...(entityTypeSlug === 'service-provider' && service_provider_details && { entityDetailsServiceProvider: { create: service_provider_details } }), // New
            ...(entityTypeSlug === 'investor' && investor_details && { entityDetailsInvestor: { create: investor_details } }), // New
            ...(entityTypeSlug === 'event' && event_details && { entityDetailsEvent: { create: event_details } }), // New
            ...(entityTypeSlug === 'job' && job_details && { entityDetailsJob: { create: job_details } }), // New
            ...(entityTypeSlug === 'grant' && grant_details && { entityDetailsGrant: { create: grant_details } }), // New
            ...(entityTypeSlug === 'bounty' && bounty_details && { entityDetailsBounty: { create: bounty_details } }), // New
            ...(entityTypeSlug === 'hardware' && hardware_details && { entityDetailsHardware: { create: hardware_details } }), // New
            ...(entityTypeSlug === 'news' && news_details && { entityDetailsNews: { create: news_details } }), // New
            ...(entityTypeSlug === 'book' && book_details && { entityDetailsBook: { create: book_details } }), // New
            ...(entityTypeSlug === 'podcast' && podcast_details && { entityDetailsPodcast: { create: podcast_details } }), // New
            ...(entityTypeSlug === 'platform' && platform_details && { entityDetailsPlatform: { create: platform_details } }), // New
                        
            entityCategories: category_ids
              ? { create: category_ids.map(catId => ({ categoryId: catId, assignedBy: submitterUser.id })) } 
              : undefined,
            entityTags: tag_ids
              ? { create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: submitterUser.id })) }
              : undefined,
            entityFeatures: feature_ids
              ? { create: feature_ids.map(featId => ({ featureId: featId, assignedBy: submitterUser.id })) }
              : undefined,
          };
  
          this.logger.debug(`[EntitiesService Create] createData object: ${JSON.stringify(createData, null, 2)}`);
  
          const newEntity = await tx.entity.create({
            data: createData,
            include: {
              entityType: true,
              submitter: { 
                select: { 
                  id: true, 
                  authUserId: true,
                  email: true,
                  createdAt: true,
                  lastLogin: true,
                  username: true,
                  displayName: true,
                  profilePictureUrl: true 
                }
              },
              entityCategories: { include: { category: true } },
              entityTags: { include: { tag: true } },
              entityFeatures: { include: { feature: true } },
              // Include all possible details for the response
              entityDetailsTool: true,
              entityDetailsCourse: true,
              entityDetailsAgency: true,
              entityDetailsContentCreator: true,
              entityDetailsCommunity: true,
              entityDetailsNewsletter: true,
              entityDetailsDataset: true, // New
              entityDetailsResearchPaper: true, // New
              entityDetailsSoftware: true, // New
              entityDetailsModel: true, // New
              entityDetailsProjectReference: true, // New
              entityDetailsServiceProvider: true, // New
              entityDetailsInvestor: true, // New
              entityDetailsEvent: true, // New
              entityDetailsJob: true, // New
              entityDetailsGrant: true, // New
              entityDetailsBounty: true, // New
              entityDetailsHardware: true, // New
              entityDetailsNews: true, // New
              entityDetailsBook: true, // New
              entityDetailsPodcast: true, // New
              entityDetailsPlatform: true, // New
            },
          });
          return newEntity;
    });
  }

  async findAll(listEntitiesDto: ListEntitiesDto): Promise<PaginatedResponse<Entity>> {
    const {
      page = 1,
      limit = 10,
      status,
      entityTypeIds, // New field
      categoryIds,
      tagIds,
      featureIds, // <<< Added featureIds here
      searchTerm, 
      sortBy = 'createdAt',
      sortOrder = Prisma.SortOrder.desc,
      submitterId,
      createdAtFrom,
      createdAtTo,
    } = listEntitiesDto;

    const where: Prisma.EntityWhereInput = {};

    if (status) {
      where.status = status;
    }

    if (entityTypeIds && entityTypeIds.length > 0) {
      where.entityTypeId = {
        in: entityTypeIds,
      };
    }

    if (categoryIds && categoryIds.length > 0) {
      where.entityCategories = {
        some: {
          categoryId: {
            in: categoryIds,
          },
        },
      };
    }

    if (tagIds && tagIds.length > 0) {
      where.entityTags = {
        some: {
          tagId: {
            in: tagIds,
          },
        },
      };
    }

    // Add filtering for featureIds
    if (featureIds && featureIds.length > 0) {
      where.entityFeatures = {
        some: {
          featureId: {
            in: featureIds,
          },
        },
      };
    }

    if (submitterId) {
      where.submitterId = submitterId;
    }

    if (createdAtFrom || createdAtTo) {
      where.createdAt = {};
      if (createdAtFrom) {
        where.createdAt.gte = createdAtFrom;
      }
      if (createdAtTo) {
        where.createdAt.lte = createdAtTo;
      }
    }

    if (searchTerm) {
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { shortDescription: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } },
      ];
        }

    const skip = (page - 1) * limit;

    try {
      const [entities, totalCount] = await this.prisma.$transaction([
        this.prisma.entity.findMany({
          where,
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            entityType: true,
            submitter: { 
              select: { 
                id: true, 
                authUserId: true,
                email: true,
                createdAt: true,
                lastLogin: true,
                username: true,
                displayName: true,
                profilePictureUrl: true 
              }
            },
            entityCategories: { include: { category: true } },
            entityTags: { include: { tag: true } },
            entityFeatures: { include: { feature: true } },
            reviews: { // Include reviews to calculate average rating, if not directly stored
              where: { status: ReviewStatus.APPROVED }, // Only approved reviews count
              select: { rating: true }
            },
             // Conditionally include detail tables if needed later, or ensure DTO maps them
            entityDetailsTool: true,
            entityDetailsCourse: true,
             entityDetailsAgency: true,
             entityDetailsContentCreator: true,
             entityDetailsCommunity: true,
             entityDetailsNewsletter: true,
          },
        }),
        this.prisma.entity.count({ where }),
      ]);

      // Map entities to include average rating if necessary, or handle in DTO/presenter layer
      const entitiesWithAvgRating = entities.map(entity => {
        const totalRating = entity.reviews.reduce((sum, review) => sum + review.rating, 0);
        const avgRating = entity.reviews.length > 0 ? totalRating / entity.reviews.length : 0;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { reviews, ...restOfEntity } = entity; // Exclude raw reviews from final response
        return {
          ...restOfEntity,
          avgRating: parseFloat(avgRating.toFixed(1)), // Ensure it's a number with one decimal place
          reviewsCount: entity.reviews.length,
        };
      });

      return {
        data: entitiesWithAvgRating,
        total: totalCount,
        page: page,
        limit: limit,
        totalPages: Math.ceil(totalCount / limit),
      };
    } catch (error) {
      this.logger.error(`Error in findAll entities: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Could not fetch entities.');
    }
  }

  async findOne(id: string): Promise<Entity | null> {
    const entity = await this.prisma.entity.findUnique({
      where: { id },
      include: {
        entityType: true,
        submitter: { 
          select: { 
            id: true, 
            authUserId: true,
            email: true,
            createdAt: true,
            lastLogin: true,
            username: true,
            displayName: true,
            profilePictureUrl: true 
          }
        },
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityFeatures: { include: { feature: true } },
        reviews: {
          where: { status: ReviewStatus.APPROVED },
          orderBy: { createdAt: 'desc' },
          include: { 
            user: { select: { id: true, username: true, profilePictureUrl: true } },
            reviewVotes: true,
          }
        },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
        entityDetailsDataset: true,
        entityDetailsResearchPaper: true,
        entityDetailsSoftware: true,
        entityDetailsModel: true,
        entityDetailsProjectReference: true,
        entityDetailsServiceProvider: true,
        entityDetailsInvestor: true,
        entityDetailsEvent: true,
        entityDetailsJob: true,
        entityDetailsGrant: true,
        entityDetailsBounty: true,
        entityDetailsHardware: true,
        entityDetailsNews: true,
        entityDetailsBook: true,
        entityDetailsPodcast: true,
        entityDetailsPlatform: true,
      },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // No second query needed if all details are included above.
    // The correct detail table will be populated, others will be null.
    return entity;
  }

  async update(id: string, updateEntityDto: UpdateEntityDto, currentUser: UserModel): Promise<Entity> {
    const {
      // Destructure all direct entity fields from DTO
      name,
      website_url,
      // entity_type_id cannot be updated directly, handle via specific admin endpoint if needed
      short_description,
      description,
      logo_url,
      documentation_url,
      contact_url,
      privacy_policy_url,
      founded_year,
      social_links,
      category_ids,
      tag_ids,
      feature_ids,
      status, 

      meta_title,
      meta_description,
      employee_count_range,
      funding_stage,
      location_summary,
      ref_link,
      affiliate_status,
      scraped_review_sentiment_label,
      scraped_review_sentiment_score,
      scraped_review_count,

      tool_details,
      course_details,
      agency_details,
      content_creator_details,
      community_details,
      newsletter_details,
      dataset_details,
      research_paper_details,
      software_details,
      model_details,
      project_reference_details,
      service_provider_details,
      investor_details,
      event_details,
      job_details,
      grant_details,
      bounty_details,
      hardware_details,
      news_details,
      book_details,
      podcast_details,
      platform_details,
    } = updateEntityDto;

    // Fetch the current entity with its relations to check ownership and current state
    const entity = await this.prisma.entity.findUnique({
      where: { id },
      include: {
        submitter: true,
        entityCategories: true,
        entityTags: true,
        entityFeatures: true, // Include existing features for context if needed, though deleteMany/create handles it
        entityType: true, // To get slug for details DTO mapping
      },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // Authorization: Check if the current user is the submitter or an admin/moderator
    const canUpdate = 
      entity.submitterId === currentUser.id || 
      currentUser.role === UserRole.ADMIN || 
      currentUser.role === UserRole.MODERATOR;

    if (!canUpdate) {
      throw new ForbiddenException('You do not have permission to update this entity.');
    }

    const updateData: Prisma.EntityUpdateInput = {};

    // Direct entity fields that can be updated
    if (name !== undefined) updateData.name = name;
    if (website_url !== undefined) updateData.websiteUrl = website_url;
    if (short_description !== undefined) updateData.shortDescription = short_description;
    if (description !== undefined) updateData.description = description;
    if (logo_url !== undefined) updateData.logoUrl = logo_url;
    if (documentation_url !== undefined) updateData.documentationUrl = documentation_url;
    if (contact_url !== undefined) updateData.contactUrl = contact_url;
    if (privacy_policy_url !== undefined) updateData.privacyPolicyUrl = privacy_policy_url;
    if (founded_year !== undefined) updateData.foundedYear = founded_year;
    if (social_links !== undefined) updateData.socialLinks = social_links as Prisma.InputJsonValue | undefined;
    
    if (meta_title !== undefined) updateData.metaTitle = meta_title;
    if (meta_description !== undefined) updateData.metaDescription = meta_description;
    if (employee_count_range !== undefined) updateData.employeeCountRange = employee_count_range;
    if (funding_stage !== undefined) updateData.fundingStage = funding_stage;
    if (location_summary !== undefined) updateData.locationSummary = location_summary;
    if (ref_link !== undefined) updateData.refLink = ref_link;
    if (affiliate_status !== undefined) updateData.affiliateStatus = affiliate_status;
    if (scraped_review_sentiment_label !== undefined) updateData.scrapedReviewSentimentLabel = scraped_review_sentiment_label;
    if (scraped_review_sentiment_score !== undefined) updateData.scrapedReviewSentimentScore = scraped_review_sentiment_score;
    if (scraped_review_count !== undefined) updateData.scrapedReviewCount = scraped_review_count;

    // Handle status updates - ensuring only admin/moderator can set to certain statuses
    if (status !== undefined) {
      if (
        currentUser.role === UserRole.ADMIN ||
        currentUser.role === UserRole.MODERATOR
      ) {
        updateData.status = status;
      } else if (status === EntityStatus.PENDING && entity.status === EntityStatus.NEEDS_REVISION) {
        // User resubmitting after 'NEEDS_REVISION'
        updateData.status = EntityStatus.PENDING;
      } else if (status !== entity.status) {
        this.logger.warn(
          `User ${currentUser.id} attempted to change status of entity ${id} from ${entity.status} to ${status} without ADMIN/MODERATOR role.`,
        );
      }
    }

    // Handle category_ids update: disconnect all, then connect new ones
    if (category_ids !== undefined) {
      updateData.entityCategories = {
        deleteMany: {},
        create: category_ids.map(catId => ({ categoryId: catId, assignedBy: currentUser.id })),
      };
    }

    // Handle tag_ids update: disconnect all, then connect new ones
    if (tag_ids !== undefined) {
      updateData.entityTags = {
        deleteMany: {},
        create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: currentUser.id })),
      };
    }

    // Handle feature_ids update: disconnect all, then connect new ones
    if (feature_ids !== undefined) {
      updateData.entityFeatures = {
        deleteMany: {},
        create: feature_ids.map(featId => ({ featureId: featId, assignedBy: currentUser.id })),
      };
    }

        // Type-specific details update logic
        const entityTypeSlug = entity.entityType.slug;
        if (entityTypeSlug === 'ai-tool' && tool_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(tool_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsTool = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsToolUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsToolUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'online-course' && course_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(course_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsCourse = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsCourseUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsCourseUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'agency' && agency_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(agency_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsAgency = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsAgencyUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsAgencyUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'content-creator' && content_creator_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(content_creator_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsContentCreator = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsContentCreatorUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsContentCreatorUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'community' && community_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(community_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsCommunity = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsCommunityUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsCommunityUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'newsletter' && newsletter_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(newsletter_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsNewsletter = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsNewsletterUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsNewsletterUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'dataset' && dataset_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(dataset_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsDataset = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsDatasetUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsDatasetUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'research-paper' && research_paper_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(research_paper_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsResearchPaper = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsResearchPaperUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsResearchPaperUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'software' && software_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(software_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsSoftware = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsSoftwareUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsSoftwareUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'model' && model_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(model_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsModel = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsModelUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsModelUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'project-reference' && project_reference_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(project_reference_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsProjectReference = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsProjectReferenceUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsProjectReferenceUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'service-provider' && service_provider_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(service_provider_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsServiceProvider = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsServiceProviderUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsServiceProviderUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'investor' && investor_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(investor_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsInvestor = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsInvestorUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsInvestorUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'event' && event_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(event_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsEvent = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsEventUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsEventUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'job' && job_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(job_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsJob = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsJobUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsJobUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'grant' && grant_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(grant_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsGrant = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsGrantUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsGrantUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'bounty' && bounty_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(bounty_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsBounty = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsBountyUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsBountyUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'hardware' && hardware_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(hardware_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsHardware = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsHardwareUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsHardwareUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'news' && news_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(news_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsNews = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsNewsUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsNewsUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'book' && book_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(book_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsBook = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsBookUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsBookUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'podcast' && podcast_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(podcast_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsPodcast = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsPodcastUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsPodcastUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'platform' && platform_details) {
          const detailDataForPrisma = this.mapSharedDetailsToPrisma(platform_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsPlatform = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsPlatformUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsPlatformUpdateInput,
              },
            };
          }
        }

    return this.prisma.$transaction(async (tx) => {
      if (Object.keys(updateData).length > 0) {
        const updatedEntity = await tx.entity.update({
          where: { id },
          data: updateData,
          include: {
            entityType: true,
            submitter: { 
              select: { 
                id: true, 
                authUserId: true,
                email: true,
                createdAt: true,
                lastLogin: true,
                username: true,
                displayName: true,
                profilePictureUrl: true 
              }
            },
            entityCategories: { include: { category: true } },
            entityTags: { include: { tag: true } },
            entityFeatures: { include: { feature: true } },
            entityDetailsTool: true,
            entityDetailsCourse: true,
            entityDetailsAgency: true,
            entityDetailsContentCreator: true,
            entityDetailsCommunity: true,
            entityDetailsNewsletter: true,
            entityDetailsDataset: true,
            entityDetailsResearchPaper: true,
            entityDetailsSoftware: true,
            entityDetailsModel: true,
            entityDetailsProjectReference: true,
            entityDetailsServiceProvider: true,
            entityDetailsInvestor: true,
            entityDetailsEvent: true,
            entityDetailsJob: true,
            entityDetailsGrant: true,
            entityDetailsBounty: true,
            entityDetailsHardware: true,
            entityDetailsNews: true,
            entityDetailsBook: true,
            entityDetailsPodcast: true,
            entityDetailsPlatform: true,
          },
        });
        return updatedEntity;
      }
      return entity;
    });
  }

  async adminSetStatus(id: string, newStatus: EntityStatus): Promise<Entity> {
    const entityExists = await this.prisma.entity.findUnique({ 
      where: { id },
      // Minimal include to check existence, actual includes for response are done in update
    });
    if (!entityExists) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // Potentially add more specific business logic/validation for admin status changes here
    // For example, if certain statuses can only transition from/to specific other statuses.
    // Or if changing status requires other fields to be updated/nulled.

    this.logger.log(`[EntitiesService AdminSetStatus] Admin changing status of entity ${id} to ${newStatus}`);

    return this.prisma.entity.update({
      where: { id },
      data: { 
        status: newStatus, 
        updatedAt: new Date(), // Explicitly set updatedAt
        // Consider if an admin_action_log or similar should be created here
      },
      include: { // Ensure this include matches what your EntityResponseDto expects
        entityType: true,
        submitter: { 
          select: { 
            id: true, 
            authUserId: true,
            email: true,
            createdAt: true,
            lastLogin: true,
            username: true,
            displayName: true,
            profilePictureUrl: true 
          }
        },
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
        entityDetailsDataset: true,
        entityDetailsResearchPaper: true,
        entityDetailsSoftware: true,
        entityDetailsModel: true,
        entityDetailsProjectReference: true,
        entityDetailsServiceProvider: true,
        entityDetailsInvestor: true,
        entityDetailsEvent: true,
        entityDetailsJob: true,
        entityDetailsGrant: true,
        entityDetailsBounty: true,
        entityDetailsHardware: true,
        entityDetailsNews: true,
        entityDetailsBook: true,
        entityDetailsPodcast: true,
        entityDetailsPlatform: true,
        reviews: { 
          where: { status: ReviewStatus.APPROVED }, 
          orderBy: { createdAt: 'desc' },
          take: 5, 
          include: {
            user: {
              select: {
                id: true,
                displayName: true,
                profilePictureUrl: true,
              }
            }
          }
        },
        _count: {
          select: {
            reviews: { where: { status: ReviewStatus.APPROVED } },
          }
        }
      },
    });
  }

  async remove(id: string, currentUser: UserModel): Promise<void> {
    console.log(
      `[DELETE /entities/${id}] Called by user: ${currentUser.id}, role: ${currentUser.role}`,
    );

    if (currentUser.role !== UserRole.ADMIN) {
      console.log(`[DELETE /entities/${id}] Auth failed: User not admin.`);
      throw new ForbiddenException(
        'You are not authorized to archive this entity.',
      );
    }

    const entityToArchive = await this.prisma.entity.findUnique({
      where: { id },
      select: { id: true, status: true },
    });
    console.log(`[DELETE /entities/${id}] Found entity:`, entityToArchive);

    if (!entityToArchive) {
      console.log(`[DELETE /entities/${id}] Entity not found.`);
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    if (entityToArchive.status === EntityStatus.ARCHIVED) {
      console.log(`[DELETE /entities/${id}] Entity already archived.`);
      return;
    }

    try {
      console.log(
        `[DELETE /entities/${id}] Attempting to archive entity ${id}...`,
      );
      const result = await this.prisma.entity.update({
        where: { id },
        data: {
          status: EntityStatus.ARCHIVED,
          updatedAt: new Date(),
        },
      });
      console.log(`[DELETE /entities/${id}] Prisma update result:`, result);
    } catch (error) {
      console.error(
        `[DELETE /entities/${id}] Failed to archive entity ${id}:`,
        error,
      );
      throw new InternalServerErrorException('Could not archive the entity.');
    }
  }
} 