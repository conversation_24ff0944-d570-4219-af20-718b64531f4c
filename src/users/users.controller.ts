import { Controller, Get, Post, Body, Patch, Param, Delete, Put, UseGuards, NotFoundException, HttpCode, HttpStatus } from '@nestjs/common';
import { UserService } from './users.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'; // Adjust path if needed
import { GetUser } from '../auth/decorators/get-user.decorator'; // Adjust path if needed
// Import the User type from the generated Prisma client
import { User as UserModel, UserNotificationSettings as UserNotificationSettingsModel, UserRole, UserStatus } from '../../generated/prisma'; // Use 'as' to avoid naming conflict with @GetUser parameter
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { UserNotificationSettingsResponseDto } from './dto/user-notification-settings-response.dto';

@ApiTags('Current User') // Changed tag for clarity
@ApiBearerAuth() 
@Controller('users') // Base path usually /users, specific to current user is /users/me
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: "Get current authenticated user\'s profile",
    description: "Retrieves the detailed profile information for the currently logged-in user."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Current user\'s profile retrieved successfully.", type: UserProfileResponseDto })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User profile not found in the database. This might indicate an issue with data consistency or if the user was recently deleted.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async getMyProfile(@GetUser() user: UserModel): Promise<UserProfileResponseDto> { 
    const profile = await this.userService.findProfileById(user.id);
    if (!profile) {
      throw new NotFoundException('User profile not found. User might not exist in public table or JWT strategy is incorrect.');
    }
    return {
      id: profile.id,
      authUserId: profile.authUserId,
      email: profile.email,
      username: profile.username,
      displayName: profile.displayName,
      profilePictureUrl: profile.profilePictureUrl,
      bio: profile.bio,
      status: profile.status as UserStatus,
      role: profile.role as UserRole,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt,
      lastLoginAt: profile.lastLogin, 
    };
  }

  @Put('me')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: "Update current authenticated user\'s profile",
    description: "Allows the currently logged-in user to update their profile information. Certain fields like email, role, or status might be restricted or handled by separate processes."
  })
  @ApiBody({ type: UpdateProfileDto })
  @ApiResponse({ status: HttpStatus.OK, description: "User profile updated successfully.", type: UserProfileResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., username format, length constraints) or username conflict if attempting to change to an existing username.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found during update attempt.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async updateMyProfile(
    @GetUser() user: UserModel, 
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<UserProfileResponseDto> { 
    const updatedProfile = await this.userService.updateProfile(user.id, updateProfileDto);
    return {
      id: updatedProfile.id,
      authUserId: updatedProfile.authUserId,
      email: updatedProfile.email,
      username: updatedProfile.username,
      displayName: updatedProfile.displayName,
      profilePictureUrl: updatedProfile.profilePictureUrl,
      bio: updatedProfile.bio,
      status: updatedProfile.status as UserStatus,
      role: updatedProfile.role as UserRole,
      createdAt: updatedProfile.createdAt,
      updatedAt: updatedProfile.updatedAt,
      lastLoginAt: updatedProfile.lastLogin,
    };
  }

  @Get('me/preferences')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: "Get current authenticated user\'s notification preferences",
    description: "Retrieves the notification settings for the currently logged-in user."
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Notification preferences retrieved successfully.", type: UserNotificationSettingsResponseDto })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Notification settings not found for this user (they may not have been created yet).' })
  async getMyNotificationSettings(@GetUser() user: UserModel): Promise<UserNotificationSettingsResponseDto | null> {
    const settings = await this.userService.findNotificationSettingsByUserId(user.id);
    if (!settings) return null;
    return {
      id: settings.userId,
      userId: settings.userId,
      newCommentOnEntity: settings.emailNewEntityInFollowedCategory,
      replyToComment: settings.emailNewsletter,
      entityStatusChange: settings.emailUpdatesOnSavedEntity,
      newReviewOnEntity: settings.emailNewReviewOnSavedEntity,
      platformAnnouncements: settings.emailNewsletter,
      digestSubscription: settings.emailNewsletter,
      createdAt: settings.createdAt,
      updatedAt: settings.updatedAt,
    };
  }

  @Put('me/preferences')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: "Update current authenticated user\'s notification preferences",
    description: "Allows the currently logged-in user to update their notification settings. Settings are typically created on first update if they don\'t exist."
  })
  @ApiBody({ type: UpdateNotificationSettingsDto })
  @ApiResponse({ status: HttpStatus.OK, description: "Notification preferences updated successfully.", type: UserNotificationSettingsResponseDto })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data for notification settings.' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  async updateMyNotificationSettings(
    @GetUser() user: UserModel,
    @Body() updateDto: UpdateNotificationSettingsDto,
  ): Promise<UserNotificationSettingsResponseDto> {
    const updatedSettings = await this.userService.updateNotificationSettings(user.id, updateDto);
    return {
      id: updatedSettings.userId,
      userId: updatedSettings.userId,
      newCommentOnEntity: updatedSettings.emailNewEntityInFollowedCategory,
      replyToComment: updatedSettings.emailNewsletter,
      entityStatusChange: updatedSettings.emailUpdatesOnSavedEntity,
      newReviewOnEntity: updatedSettings.emailNewReviewOnSavedEntity,
      platformAnnouncements: updatedSettings.emailNewsletter,
      digestSubscription: updatedSettings.emailNewsletter,
      createdAt: updatedSettings.createdAt,
      updatedAt: updatedSettings.updatedAt,
    };
  }

  @Delete('me')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK) // Or 204 No Content if nothing is returned in body
  @ApiOperation({ 
    summary: "Soft delete current authenticated user\'s account",
    description: "Marks the current user\'s account for deletion. The actual deletion and data purging process might be asynchronous and subject to retention policies. This action requires the user\'s current JWT."
   })
  @ApiResponse({ status: HttpStatus.OK, description: 'Account successfully scheduled for deletion.', schema: { example: { message: 'Account successfully scheduled for deletion. ...'}} })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Failed to process account deletion due to an internal error.' })
  async softDeleteMyAccount(@GetUser() user: UserModel): Promise<{ message: string }> { 
    await this.userService.softDeleteUser(user.id, user.authUserId);
    return { message: 'Account successfully scheduled for deletion. All related data will be processed according to our retention policies.' };
  }

  // --- Placeholder CRUD generated by Nest CLI - Keep or remove as needed ---
  /*
  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    // Typically user creation is handled by AuthModule/Signup
    // return this.usersService.create(createUserDto);
  }

  @Get()
  findAll() {
    // Might be an admin endpoint
    // return this.usersService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    // Might be an admin endpoint or for viewing public profiles
    // return this.usersService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    // Might be an admin endpoint
    // return this.usersService.update(+id, updateUserDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    // Might be an admin endpoint
    // return this.usersService.remove(+id);
  }
  */
  // --- End Placeholder CRUD ---
}
