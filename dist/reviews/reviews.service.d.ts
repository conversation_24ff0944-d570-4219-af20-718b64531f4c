import { PrismaService } from '../prisma/prisma.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { Review, ReviewStatus, User as UserModel } from 'generated/prisma';
import { UpdateReviewDto } from './dto/update-review.dto';
export declare class ReviewsService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    createReview(userId: string, entityId: string, createReviewDto: CreateReviewDto): Promise<Review>;
    updateUserReview(reviewId: string, userId: string, updateReviewDto: UpdateReviewDto): Promise<Review>;
    deleteUserReview(reviewId: string, userId: string): Promise<void>;
    getApprovedReviewsForEntity(entityId: string, listReviewsDto: ListReviewsDto): Promise<{
        data: Partial<Review & {
            user: {
                username: string | null;
                profilePictureUrl: string | null;
                id: string;
            };
        }>[];
        total: number;
        page: number;
        limit: number;
    }>;
    updateReviewStatus(reviewId: string, newStatus: ReviewStatus, adminUser: UserModel): Promise<Review>;
    adminDeleteReview(reviewId: string, adminUser: UserModel): Promise<void>;
}
