import { ReviewsService } from './reviews.service';
import { UpdateReviewStatusDto } from './dto/update-review-status.dto';
export declare class AdminReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    updateReviewStatus(id: string, updateReviewStatusDto: UpdateReviewStatusDto, req: any): Promise<{
        id: string;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        createdAt: Date;
        updatedAt: Date;
        entityId: string;
        userId: string;
        rating: number;
        title: string | null;
        reviewText: string | null;
        helpfulnessScore: number;
        moderatorUserId: string | null;
        moderatedAt: Date | null;
        moderationNotes: string | null;
    }>;
    adminDeleteReview(id: string, req: any): Promise<void>;
}
