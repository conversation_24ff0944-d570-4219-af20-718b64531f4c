import { TestService } from './test.service';
export declare class TestController {
    private readonly testService;
    constructor(testService: TestService);
    testEntityTypeCrud(): Promise<{
        createReadResult: {
            createdType: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                description: string | null;
                slug: string;
                iconUrl: string | null;
            };
            readType: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                description: string | null;
                slug: string;
                iconUrl: string | null;
            } | null;
            allTypesCount: number;
        };
        updateResult: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
        deleteResult: {
            message: string;
            deleted: boolean;
        };
    }>;
    testEntityRelation(): Promise<{
        entityType: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
    } & {
        name: string;
        id: string;
        status: import("@generated-prisma").$Enums.EntityStatus;
        socialLinks: import("@generated-prisma/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        websiteUrl: string | null;
        entityTypeId: string;
        shortDescription: string | null;
        description: string | null;
        logoUrl: string | null;
        documentationUrl: string | null;
        contactUrl: string | null;
        privacyPolicyUrl: string | null;
        foundedYear: number | null;
        submitterId: string;
        legacyId: string | null;
        reviewCount: number;
        avgRating: number;
        metaTitle: string | null;
        metaDescription: string | null;
        scrapedReviewSentimentLabel: string | null;
        scrapedReviewSentimentScore: number | null;
        scrapedReviewCount: number | null;
        employeeCountRange: string | null;
        fundingStage: string | null;
        locationSummary: string | null;
        refLink: string | null;
        affiliateStatus: import("@generated-prisma").$Enums.AffiliateStatus | null;
    }>;
    testEntityTagM2MRelation(): Promise<{
        finalEntity: {
            entityTags: ({
                tag: {
                    name: string;
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    description: string | null;
                    slug: string;
                    iconUrl: string | null;
                };
            } & {
                entityId: string;
                tagId: string;
            })[];
        } & {
            name: string;
            id: string;
            status: import("@generated-prisma").$Enums.EntityStatus;
            socialLinks: import("@generated-prisma/runtime/library").JsonValue | null;
            createdAt: Date;
            updatedAt: Date;
            websiteUrl: string | null;
            entityTypeId: string;
            shortDescription: string | null;
            description: string | null;
            logoUrl: string | null;
            documentationUrl: string | null;
            contactUrl: string | null;
            privacyPolicyUrl: string | null;
            foundedYear: number | null;
            submitterId: string;
            legacyId: string | null;
            reviewCount: number;
            avgRating: number;
            metaTitle: string | null;
            metaDescription: string | null;
            scrapedReviewSentimentLabel: string | null;
            scrapedReviewSentimentScore: number | null;
            scrapedReviewCount: number | null;
            employeeCountRange: string | null;
            fundingStage: string | null;
            locationSummary: string | null;
            refLink: string | null;
            affiliateStatus: import("@generated-prisma").$Enums.AffiliateStatus | null;
        };
        tagSlugs: string[];
    }>;
    testEnumConstraints(): Promise<{
        message: string;
    }>;
    testUniqueConstraints(): Promise<{
        message: string;
    }>;
    testOptionalDefaultsAndTimestamps(): Promise<{
        message: string;
        initial: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
        updated: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
    }>;
}
