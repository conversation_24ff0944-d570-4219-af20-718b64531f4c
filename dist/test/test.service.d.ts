import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '../../generated/prisma';
export declare class TestService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    testCreateReadEntityType(): Promise<{
        createdType: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
        readType: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        } | null;
        allTypesCount: number;
    }>;
    testUpdateEntityType(slug: string): Promise<{
        name: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        slug: string;
        iconUrl: string | null;
    }>;
    testDeleteEntityType(slug: string): Promise<{
        message: string;
        deleted: boolean;
    }>;
    testCreateEntityWithRelation(): Promise<{
        entityType: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
    } & {
        name: string;
        id: string;
        status: import("../../generated/prisma").$Enums.EntityStatus;
        socialLinks: Prisma.JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        websiteUrl: string | null;
        entityTypeId: string;
        shortDescription: string | null;
        description: string | null;
        logoUrl: string | null;
        documentationUrl: string | null;
        contactUrl: string | null;
        privacyPolicyUrl: string | null;
        foundedYear: number | null;
        submitterId: string;
        legacyId: string | null;
        reviewCount: number;
        avgRating: number;
        metaTitle: string | null;
        metaDescription: string | null;
        scrapedReviewSentimentLabel: string | null;
        scrapedReviewSentimentScore: number | null;
        scrapedReviewCount: number | null;
        employeeCountRange: string | null;
        fundingStage: string | null;
        locationSummary: string | null;
        refLink: string | null;
        affiliateStatus: import("../../generated/prisma").$Enums.AffiliateStatus | null;
    }>;
    testEntityTagRelation(): Promise<{
        finalEntity: {
            entityTags: ({
                tag: {
                    name: string;
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    description: string | null;
                    slug: string;
                    iconUrl: string | null;
                };
            } & {
                entityId: string;
                tagId: string;
            })[];
        } & {
            name: string;
            id: string;
            status: import("../../generated/prisma").$Enums.EntityStatus;
            socialLinks: Prisma.JsonValue | null;
            createdAt: Date;
            updatedAt: Date;
            websiteUrl: string | null;
            entityTypeId: string;
            shortDescription: string | null;
            description: string | null;
            logoUrl: string | null;
            documentationUrl: string | null;
            contactUrl: string | null;
            privacyPolicyUrl: string | null;
            foundedYear: number | null;
            submitterId: string;
            legacyId: string | null;
            reviewCount: number;
            avgRating: number;
            metaTitle: string | null;
            metaDescription: string | null;
            scrapedReviewSentimentLabel: string | null;
            scrapedReviewSentimentScore: number | null;
            scrapedReviewCount: number | null;
            employeeCountRange: string | null;
            fundingStage: string | null;
            locationSummary: string | null;
            refLink: string | null;
            affiliateStatus: import("../../generated/prisma").$Enums.AffiliateStatus | null;
        };
        tagSlugs: string[];
    }>;
    testEnums(): Promise<{
        message: string;
    }>;
    testUniqueConstraints(): Promise<{
        message: string;
    }>;
    testOptionalDefaultsUpdatedAt(): Promise<{
        message: string;
        initial: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
        updated: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            slug: string;
            iconUrl: string | null;
        };
    }>;
    private ensureTestEntityType;
    private ensureTestTag;
    cleanupM2MTestData(entityId: string, tagIds: string[], createdTagSlug?: string): Promise<void>;
}
