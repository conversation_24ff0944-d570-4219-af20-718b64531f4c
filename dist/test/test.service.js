"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
let TestService = class TestService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async testCreateReadEntityType() {
        const slug = 'test-type-prisma-' + Date.now();
        try {
            console.log(`Attempting to create EntityType with slug: ${slug}`);
            const createdType = await this.prisma.entityType.create({
                data: { name: 'Test Type From Prisma', slug: slug, description: 'Initial description' },
            });
            console.log('Created EntityType:', createdType);
            console.log(`Attempting to read EntityType with slug: ${slug}`);
            const readType = await this.prisma.entityType.findUnique({
                where: { slug: slug },
            });
            console.log('Read EntityType:', readType);
            console.log('Attempting to read all EntityTypes');
            const allTypes = await this.prisma.entityType.findMany();
            console.log('All EntityTypes count:', allTypes.length);
            return { createdType, readType, allTypesCount: allTypes.length };
        }
        catch (error) {
            console.error('Error during CREATE/READ test:', error);
            throw new common_1.InternalServerErrorException('CREATE/READ test failed', error.message);
        }
    }
    async testUpdateEntityType(slug) {
        try {
            console.log(`Attempting to update EntityType with slug: ${slug}`);
            const updatedType = await this.prisma.entityType.update({
                where: { slug: slug },
                data: { description: 'Updated description at ' + new Date().toISOString() },
            });
            console.log('Updated EntityType:', updatedType);
            return updatedType;
        }
        catch (error) {
            console.error(`Error updating EntityType ${slug}:`, error);
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                throw new common_1.NotFoundException(`EntityType with slug '${slug}' not found for update.`);
            }
            throw new common_1.InternalServerErrorException('UPDATE test failed', error.message);
        }
    }
    async testDeleteEntityType(slug) {
        try {
            console.log(`Attempting to delete EntityType with slug: ${slug}`);
            await this.prisma.entityType.delete({
                where: { slug: slug },
            });
            console.log(`Deleted EntityType with slug: ${slug}`);
            const deletedType = await this.prisma.entityType.findUnique({
                where: { slug: slug },
            });
            if (deletedType === null) {
                console.log('Deletion verified.');
                return { message: `EntityType '${slug}' deleted successfully.`, deleted: true };
            }
            else {
                console.error('Deletion verification failed!');
                throw new common_1.InternalServerErrorException('Deletion verification failed');
            }
        }
        catch (error) {
            console.error(`Error deleting EntityType ${slug}:`, error);
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                console.log(`EntityType with slug '${slug}' not found for deletion (might be already deleted).`);
                return { message: `EntityType '${slug}' not found (likely already deleted).`, deleted: true };
            }
            throw new common_1.InternalServerErrorException('DELETE test failed', error.message);
        }
    }
    async testCreateEntityWithRelation() {
        let entityType;
        const typeSlug = 'test-rel-type-prisma';
        try {
            entityType = await this.ensureTestEntityType(typeSlug, 'Test Relation Type');
            if (!entityType)
                throw new common_1.InternalServerErrorException('Failed to ensure EntityType');
            const entityName = 'Test Entity with Prisma Relation ' + Date.now();
            console.log(`Attempting to create Entity: ${entityName} linked to Type ID: ${entityType.id}`);
            const createdEntity = await this.prisma.entity.create({
                data: {
                    name: entityName,
                    websiteUrl: 'https://relation-test.example.com',
                    status: prisma_1.EntityStatus.ACTIVE,
                    description: 'Testing relations',
                    entityType: { connect: { id: entityType.id } },
                    submitter: { connect: { id: '123e4567-e89b-12d3-a456-426614174000' } }
                },
                include: { entityType: true }
            });
            console.log('Created Entity with relation:', createdEntity);
            return createdEntity;
        }
        catch (error) {
            console.error('Error during CREATE with relation test:', error);
            throw new common_1.InternalServerErrorException('CREATE relation test failed', error.message);
        }
    }
    async testEntityTagRelation() {
        let entity = null;
        let tag1 = null;
        let tag2 = null;
        let createdTagSlug = null;
        const entityName = 'test-m2m-entity-' + Date.now();
        const tag1Slug = 'test-m2m-tag1';
        const tag2Slug = 'test-m2m-tag2';
        try {
            const entityType = await this.ensureTestEntityType('test-m2m-type');
            if (!entityType)
                throw new common_1.InternalServerErrorException('Failed to ensure EntityType for M2M test');
            entity = await this.prisma.entity.create({
                data: {
                    name: entityName,
                    websiteUrl: 'https://m2m-test.com',
                    status: prisma_1.EntityStatus.ACTIVE,
                    entityTypeId: entityType.id,
                    submitterId: '123e4567-e89b-12d3-a456-426614174000'
                }
            });
            console.log('Created test entity:', entity);
            tag1 = await this.ensureTestTag(tag1Slug, 'Test M2M Tag 1');
            tag2 = await this.ensureTestTag(tag2Slug, 'Test M2M Tag 2');
            console.log('Ensured test tags exist:', tag1, tag2);
            console.log(`Connecting ${tag1Slug} to ${entityName}`);
            const updatedEntity1 = await this.prisma.entity.update({
                where: { id: entity.id },
                data: {
                    entityTags: {
                        create: [{
                                tag: { connect: { id: tag1.id } }
                            }]
                    }
                },
                include: { entityTags: { include: { tag: true } } }
            });
            console.log('Updated entity after connecting tag1:', updatedEntity1);
            if (!updatedEntity1.entityTags.some((et) => et.tag.slug === tag1Slug)) {
                throw new common_1.InternalServerErrorException(`Failed to verify connection of ${tag1Slug}`);
            }
            const tag3Name = 'New Tag via Entity ' + Date.now();
            createdTagSlug = tag3Name.toLowerCase().replace(/\s+/g, '-');
            console.log(`Connecting ${tag2Slug} and creating ${createdTagSlug} for ${entityName}`);
            const updatedEntity2 = await this.prisma.entity.update({
                where: { id: entity.id },
                data: {
                    entityTags: {
                        create: [
                            { tag: { connect: { id: tag2.id } } },
                            { tag: { create: { name: tag3Name, slug: createdTagSlug } } }
                        ]
                    }
                },
                include: { entityTags: { include: { tag: true } } }
            });
            console.log('Updated entity after connecting tag2 and creating tag3:', updatedEntity2);
            if (!updatedEntity2.entityTags.some((et) => et.tag.slug === tag2Slug)) {
                throw new common_1.InternalServerErrorException(`Failed to verify connection of ${tag2Slug}`);
            }
            if (!updatedEntity2.entityTags.some((et) => et.tag.slug === createdTagSlug)) {
                throw new common_1.InternalServerErrorException(`Failed to verify creation/connection of ${createdTagSlug}`);
            }
            const finalEntity = await this.prisma.entity.findUnique({
                where: { id: entity.id },
                include: { entityTags: { include: { tag: true } } }
            });
            if (!finalEntity)
                throw new common_1.NotFoundException('Final entity not found for verification');
            console.log('Final entity with tags:', finalEntity);
            const tagSlugs = finalEntity.entityTags.map((et) => et.tag.slug);
            const tagIdsToClean = [tag1?.id, tag2?.id].filter((id) => !!id);
            if (entity)
                await this.cleanupM2MTestData(entity.id, tagIdsToClean, createdTagSlug || undefined);
            return { finalEntity, tagSlugs };
        }
        catch (error) {
            console.error('Error during M2M relation test:', error);
            const tagIdsToClean = [tag1?.id, tag2?.id].filter((id) => !!id);
            if (entity)
                await this.cleanupM2MTestData(entity.id, tagIdsToClean, createdTagSlug || undefined);
            throw new common_1.InternalServerErrorException('M2M relation test failed', error.message);
        }
    }
    async testEnums() {
        const validName = 'test-enum-entity-' + Date.now();
        const invalidStatusValue = 'INVALID_STATUS';
        let createdEntityId = null;
        try {
            const entityType = await this.ensureTestEntityType('test-enum-type');
            if (!entityType)
                throw new common_1.InternalServerErrorException('Failed to ensure EntityType for Enum test');
            console.log('Attempting to create entity with valid status ENUM');
            const createdEntity = await this.prisma.entity.create({
                data: {
                    name: validName,
                    websiteUrl: 'https://valid-enum.com',
                    status: prisma_1.EntityStatus.PENDING,
                    entityTypeId: entityType.id,
                    submitterId: '123e4567-e89b-12d3-a456-426614174000'
                }
            });
            createdEntityId = createdEntity.id;
            console.log('Created entity with valid ENUM status:', createdEntity);
            console.log('Attempting to create entity with invalid status ENUM');
            try {
                await this.prisma.entity.create({
                    data: {
                        name: 'Test Invalid Enum ' + Date.now(),
                        websiteUrl: 'https://invalid-enum.com',
                        status: invalidStatusValue,
                        entityTypeId: entityType.id,
                        submitterId: '123e4567-e89b-12d3-a456-426614174000'
                    }
                });
                throw new common_1.InternalServerErrorException('Prisma should have rejected the invalid ENUM value!');
            }
            catch (enumError) {
                if (enumError instanceof prisma_1.Prisma.PrismaClientValidationError || (enumError instanceof prisma_1.Prisma.PrismaClientKnownRequestError && enumError.message.includes('invalid input value for enum'))) {
                    console.log('Successfully caught expected error for invalid ENUM:', enumError.message);
                }
                else {
                    console.error('Caught unexpected error type for invalid enum:', enumError);
                    throw enumError;
                }
            }
            if (createdEntityId) {
                await this.prisma.entity.delete({ where: { id: createdEntityId } });
            }
            return { message: 'ENUM tests passed (valid created, invalid rejected).' };
        }
        catch (error) {
            console.error('Error during ENUM test:', error);
            if (createdEntityId)
                await this.prisma.entity.delete({ where: { id: createdEntityId } }).catch(e => console.error("Cleanup failed:", e));
            throw new common_1.InternalServerErrorException('ENUM test failed', error.message);
        }
    }
    async testUniqueConstraints() {
        const uniqueSlug = 'unique-test-type-' + Date.now();
        let createdTypeId = null;
        try {
            console.log(`Attempting to create first EntityType with unique slug: ${uniqueSlug}`);
            const firstType = await this.prisma.entityType.create({
                data: { name: 'Unique Type 1', slug: uniqueSlug }
            });
            createdTypeId = firstType.id;
            console.log('Created first type:', firstType);
            console.log(`Attempting to create second EntityType with duplicate slug: ${uniqueSlug}`);
            await this.prisma.entityType.create({
                data: { name: 'Unique Type 2', slug: uniqueSlug }
            });
            throw new common_1.InternalServerErrorException('Unique constraint test failed: Second creation succeeded unexpectedly.');
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
                console.log('Successfully caught expected unique constraint violation (P2002).');
                if (createdTypeId) {
                    await this.prisma.entityType.delete({ where: { id: createdTypeId } });
                }
                return { message: 'Unique constraint test passed (duplicate rejected).' };
            }
            else {
                console.error('Error during unique constraint test was not P2002:', error);
                if (createdTypeId)
                    await this.prisma.entityType.delete({ where: { id: createdTypeId } }).catch(e => console.error("Cleanup failed:", e));
                throw new common_1.InternalServerErrorException('Unique constraint test failed with unexpected error', error.message);
            }
        }
    }
    async testOptionalDefaultsUpdatedAt() {
        const slug = 'optional-default-test-' + Date.now();
        let createdTypeId = null;
        try {
            console.log('Creating EntityType omitting optional description');
            const createdType = await this.prisma.entityType.create({
                data: { name: 'Optional Default Test', slug: slug }
            });
            createdTypeId = createdType.id;
            console.log('Created type:', createdType);
            if (createdType.description !== null) {
                console.warn(`Warning: Optional field 'description' was not null after creation: ${createdType.description}`);
            }
            if (!createdType.createdAt || !createdType.updatedAt) {
                throw new common_1.InternalServerErrorException('Timestamp test failed: createdAt or updatedAt missing.');
            }
            const initialUpdatedAt = createdType.updatedAt;
            await new Promise(resolve => setTimeout(resolve, 50));
            console.log('Updating the EntityType to test updatedAt');
            const updatedType = await this.prisma.entityType.update({
                where: { id: createdTypeId },
                data: { name: 'Optional Default Test Updated' }
            });
            console.log('Updated type:', updatedType);
            if (updatedType.updatedAt <= initialUpdatedAt) {
                throw new common_1.InternalServerErrorException('Timestamp test failed: updatedAt did not change after update.');
            }
            console.log(`Timestamps: Initial ${initialUpdatedAt}, Updated ${updatedType.updatedAt}`);
            if (createdTypeId) {
                await this.prisma.entityType.delete({ where: { id: createdTypeId } });
            }
            return { message: 'Optional/Default/Timestamp tests passed.', initial: createdType, updated: updatedType };
        }
        catch (error) {
            console.error('Error during Optional/Default/Timestamp test:', error);
            if (createdTypeId)
                await this.prisma.entityType.delete({ where: { id: createdTypeId } }).catch(e => console.error("Cleanup failed:", e));
            throw new common_1.InternalServerErrorException('Optional/Default/Timestamp test failed', error.message);
        }
    }
    async ensureTestEntityType(slug, name) {
        let entityType = await this.prisma.entityType.findUnique({ where: { slug } });
        if (!entityType) {
            entityType = await this.prisma.entityType.create({
                data: { name: name || `Test Type ${slug}`, slug }
            });
            console.log(`Helper: Created EntityType ${slug}`);
        }
        return entityType;
    }
    async ensureTestTag(slug, name) {
        let tag = await this.prisma.tag.findUnique({ where: { slug } });
        if (!tag) {
            tag = await this.prisma.tag.create({
                data: { name: name || `Test Tag ${slug}`, slug }
            });
            console.log(`Helper: Created Tag ${slug}`);
        }
        return tag;
    }
    async cleanupM2MTestData(entityId, tagIds, createdTagSlug) {
        try {
            console.log(`Cleaning up M2M test data for entity ${entityId}`);
            await this.prisma.entityTag.deleteMany({ where: { entityId: entityId } });
            console.log(`Deleted entityTags for entity ${entityId}`);
            await this.prisma.entity.delete({ where: { id: entityId } });
            console.log(`Deleted entity ${entityId}`);
            if (createdTagSlug) {
                await this.prisma.tag.delete({ where: { slug: createdTagSlug } });
                console.log(`Deleted created tag ${createdTagSlug}`);
            }
        }
        catch (error) {
            console.error('Error during M2M cleanup:', error);
        }
    }
};
exports.TestService = TestService;
exports.TestService = TestService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TestService);
//# sourceMappingURL=test.service.js.map