"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../supabase/supabase.service");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma");
function extractUsernameFromEmail(email) {
    const atIndex = email.indexOf('@');
    if (atIndex > 0) {
        return email.substring(0, atIndex);
    }
    return email;
}
let AuthService = class AuthService {
    constructor(supabaseService, configService, prisma) {
        this.supabaseService = supabaseService;
        this.configService = configService;
        this.prisma = prisma;
    }
    async signUp(registerAuthDto) {
        const { email, password, display_name: displayName } = registerAuthDto;
        const supabase = this.supabaseService.getClient();
        console.log('[AuthService.signUp] Attempting Supabase sign up for:', email);
        const { data: signUpAuthData, error: signUpAuthError } = await supabase.auth.signUp({
            email,
            password,
            options: {
                data: {
                    display_name: displayName || extractUsernameFromEmail(email),
                }
            }
        });
        console.log('[AuthService.signUp] Supabase signUp response - authData:', JSON.stringify(signUpAuthData, null, 2));
        console.log('[AuthService.signUp] Supabase signUp response - signUpError:', JSON.stringify(signUpAuthError, null, 2));
        if (signUpAuthError) {
            if (signUpAuthError.message.toLowerCase().includes('user already registered')) {
                throw new common_1.ConflictException('User with this email already exists.');
            }
            console.error('Supabase signUp error:', signUpAuthError);
            throw new common_1.InternalServerErrorException('Failed to register user with Supabase.');
        }
        if (!signUpAuthData.user) {
            throw new common_1.InternalServerErrorException('User registration did not return user data from Supabase.');
        }
        try {
            const profile = await this.prisma.user.upsert({
                where: { authUserId: signUpAuthData.user.id },
                update: {
                    email: signUpAuthData.user.email,
                    displayName: displayName || extractUsernameFromEmail(signUpAuthData.user.email),
                },
                create: {
                    authUserId: signUpAuthData.user.id,
                    email: signUpAuthData.user.email,
                    displayName: displayName || extractUsernameFromEmail(signUpAuthData.user.email),
                    role: prisma_1.UserRole.USER,
                    status: prisma_1.UserStatus.ACTIVE,
                },
            });
            console.log(`Profile created/updated in public.users for authUserId: ${signUpAuthData.user.id}`);
        }
        catch (dbError) {
            console.error(`CRITICAL: Failed to create/update profile in public.users for authUserId ${signUpAuthData.user.id}`, dbError);
            try {
                const adminClient = this.supabaseService.getAdminClient();
                const { error: deleteError } = await adminClient.auth.admin.deleteUser(signUpAuthData.user.id);
                if (deleteError) {
                    console.error(`[COMPENSATION FAILED] Failed to delete Supabase auth user ${signUpAuthData.user.id} after profile creation failure:`, deleteError);
                }
                else {
                    console.log(`[COMPENSATION SUCCEEDED] Successfully deleted Supabase auth user ${signUpAuthData.user.id} after profile creation failure.`);
                }
            }
            catch (adminDeleteError) {
                console.error(`[COMPENSATION EXCEPTION] Exception during attempt to delete Supabase auth user ${signUpAuthData.user.id}:`, adminDeleteError);
            }
            if (dbError.code === 'P2002') {
                const target = dbError.meta?.target;
                if (target?.includes('email')) {
                    throw new common_1.ConflictException('This email is already associated with another profile in our system.');
                }
                else if (target?.includes('authUserId')) {
                    throw new common_1.ConflictException('This authentication ID is already linked to a profile.');
                }
                else if (target?.includes('username') && target !== undefined) {
                    throw new common_1.ConflictException('This username is already taken in our system.');
                }
                throw new common_1.ConflictException('A profile with some of this information already exists.');
            }
            throw new common_1.InternalServerErrorException('Registration succeeded with auth provider, but profile creation failed.');
        }
        const message = signUpAuthData.session
            ? 'Registration successful and user logged in by Supabase!'
            : 'Registration successful! Please check your email to confirm your account.';
        return {
            message,
            user: signUpAuthData.user,
        };
    }
    async signIn(loginAuthDto) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase.auth.signInWithPassword({
            email: loginAuthDto.email,
            password: loginAuthDto.password,
        });
        if (error) {
            if (error.message === 'Invalid login credentials') {
                throw new common_1.UnauthorizedException('Invalid login credentials.');
            }
            console.error("Supabase signIn error:", error);
            throw new common_1.InternalServerErrorException(error.message || 'Error during sign in.');
        }
        if (!data || !data.user || !data.session) {
            console.error('Supabase signIn: No user or session data returned', data);
            throw new common_1.InternalServerErrorException('Sign in process failed to return complete user information.');
        }
        return { user: data.user, session: data.session };
    }
    async signOut() {
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase.auth.signOut();
        if (error) {
            console.error("Error during sign out:", error);
            throw new common_1.InternalServerErrorException(error.message || 'Error during sign out.');
        }
        return { message: 'Successfully signed out.' };
    }
    async forgotPassword(forgotPasswordDto) {
        const supabase = this.supabaseService.getClient();
        const frontendBaseUrl = this.configService.get('FRONTEND_BASE_URL');
        if (!frontendBaseUrl) {
            throw new common_1.InternalServerErrorException('FRONTEND_BASE_URL is not configured.');
        }
        const { error } = await supabase.auth.resetPasswordForEmail(forgotPasswordDto.email, {
            redirectTo: `${frontendBaseUrl}/auth/update-password`,
        });
        if (error) {
            console.error("Error sending password reset email:", error);
        }
        return { message: 'If an account with this email exists, a password reset link has been sent.' };
    }
    async resetPassword(resetPasswordDto, accessToken) {
        const supabase = this.supabaseService.getClient();
        if (accessToken) {
            const { error: sessionError } = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: 'dummy'
            });
            if (sessionError) {
                console.error('Password reset: Error setting session with access token.', sessionError);
                throw new common_1.UnauthorizedException('Invalid or expired password reset link. Please request a new one.');
            }
        }
        else {
            console.warn('ResetPassword called without an explicit access token. Relying on implicit session state.');
        }
        const { error: updateError } = await supabase.auth.updateUser({
            password: resetPasswordDto.password,
        });
        if (updateError) {
            console.error("Error resetting password:", updateError);
            if (updateError.message.includes('weak password')) {
                throw new common_1.BadRequestException('Password is too weak. Please choose a stronger password.');
            }
            if (updateError.message.includes('same as the old password')) {
                throw new common_1.BadRequestException('New password cannot be the same as the old password.');
            }
            throw new common_1.InternalServerErrorException(updateError.message || 'Error resetting password.');
        }
        return { message: 'Password has been successfully reset.' };
    }
    async resendConfirmation(resendConfirmationDto) {
        const supabase = this.supabaseService.getClient();
        const frontendBaseUrl = this.configService.get('FRONTEND_BASE_URL');
        if (!frontendBaseUrl) {
            throw new common_1.InternalServerErrorException('FRONTEND_BASE_URL is not configured.');
        }
        const { error } = await supabase.auth.resend({
            type: 'signup',
            email: resendConfirmationDto.email,
            options: {
                emailRedirectTo: `${frontendBaseUrl}/auth/confirm-email`,
            }
        });
        if (error) {
            console.error("Error resending confirmation email:", error);
        }
        return { message: 'If an account with this email exists and requires confirmation, a new link has been sent.' };
    }
    handleAuthError(error) {
        console.error('Supabase Auth Error:', error.name, error.message);
        if (error.message.includes('User already registered')) {
            throw new common_1.ConflictException('User with this email already exists.');
        }
        if (error.status === 400 || error.name === 'AuthWeakPasswordError') {
            throw new common_1.BadRequestException(error.message || 'Invalid registration data or weak password.');
        }
        throw new common_1.InternalServerErrorException(error.message || 'An unexpected error occurred during authentication.');
    }
    async syncUserProfile(authUserPayload) {
        if (!authUserPayload || !authUserPayload.id || !authUserPayload.email) {
            console.error(`[AuthService] syncUserProfile - Validation FAILED. Received ID: ${authUserPayload?.id}, Received Email: ${authUserPayload?.email}`);
            throw new common_1.BadRequestException('Valid authenticated user data is required for profile sync.');
        }
        const displayNameFromMeta = authUserPayload.user_metadata?.full_name ||
            authUserPayload.user_metadata?.name ||
            authUserPayload.user_metadata?.display_name ||
            extractUsernameFromEmail(authUserPayload.email);
        try {
            const profile = await this.prisma.user.upsert({
                where: { authUserId: authUserPayload.id },
                update: {
                    email: authUserPayload.email,
                    displayName: displayNameFromMeta,
                    lastLogin: new Date(),
                },
                create: {
                    authUserId: authUserPayload.id,
                    email: authUserPayload.email,
                    displayName: displayNameFromMeta,
                    role: prisma_1.UserRole.USER,
                    status: prisma_1.UserStatus.ACTIVE,
                    lastLogin: new Date(),
                },
            });
            console.log(`Profile synced in public.users for authUserId: ${authUserPayload.id}`);
            return profile;
        }
        catch (dbError) {
            console.error(`CRITICAL: Failed to sync profile in public.users for authUserId ${authUserPayload.id}`, dbError);
            if (dbError.code === 'P2002') {
                const target = dbError.meta?.target;
                if (target?.includes('email')) {
                    throw new common_1.ConflictException('Sync failed: This email is already associated with another profile.');
                }
                else if (target?.includes('authUserId')) {
                    throw new common_1.ConflictException('Sync failed: This authentication ID is already linked to a profile.');
                }
                else if (target?.includes('username') && target !== undefined) {
                    throw new common_1.ConflictException('Sync failed: This username is already taken.');
                }
                throw new common_1.ConflictException('Sync failed: A profile conflict occurred.');
            }
            throw new common_1.InternalServerErrorException('Profile synchronization failed.');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        config_1.ConfigService,
        prisma_service_1.PrismaService])
], AuthService);
//# sourceMappingURL=auth.service.js.map