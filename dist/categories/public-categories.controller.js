"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicCategoriesController = void 0;
const common_1 = require("@nestjs/common");
const categories_service_1 = require("./categories.service");
const swagger_1 = require("@nestjs/swagger");
const category_response_dto_1 = require("./dto/category-response.dto");
let PublicCategoriesController = class PublicCategoriesController {
    constructor(categoriesService) {
        this.categoriesService = categoriesService;
    }
    mapToResponseDto(category) {
        return {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            iconUrl: category.iconUrl,
            parentCategoryId: category.parentCategoryId,
            createdAt: category.createdAt,
            updatedAt: category.updatedAt,
        };
    }
    async findAll() {
        const categories = await this.categoriesService.findAllPublic();
        return categories.map(cat => this.mapToResponseDto(cat));
    }
};
exports.PublicCategoriesController = PublicCategoriesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all public categories' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all public categories.', type: [category_response_dto_1.CategoryResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PublicCategoriesController.prototype, "findAll", null);
exports.PublicCategoriesController = PublicCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('Public - Categories'),
    (0, common_1.Controller)('categories'),
    __metadata("design:paramtypes", [categories_service_1.CategoriesService])
], PublicCategoriesController);
//# sourceMappingURL=public-categories.controller.js.map