import { OnModuleInit } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { ListEntitiesDto } from './dto/list-entities.dto';
import { Entity, User as UserModel, EntityStatus } from 'generated/prisma';
import { PaginatedResponse } from '../common/interfaces/paginated-response.interface';
export declare class EntitiesService implements OnModuleInit {
    private readonly prisma;
    private entityTypeMap;
    private readonly logger;
    constructor(prisma: PrismaService);
    onModuleInit(): Promise<void>;
    private loadEntityTypes;
    private mapSharedDetailsToPrisma;
    create(createEntityDto: CreateEntityDto, submitterUser: UserModel): Promise<Entity>;
    findAll(listEntitiesDto: ListEntitiesDto): Promise<PaginatedResponse<Entity>>;
    findOne(id: string): Promise<Entity | null>;
    update(id: string, updateEntityDto: UpdateEntityDto, currentUser: UserModel): Promise<Entity>;
    adminSetStatus(id: string, newStatus: EntityStatus): Promise<Entity>;
    remove(id: string, currentUser: UserModel): Promise<void>;
}
