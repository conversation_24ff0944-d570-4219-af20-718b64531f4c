"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PlatformDetailsResponseDto {
}
exports.PlatformDetailsResponseDto = PlatformDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'p1q2r3s4-t5u6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], PlatformDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of platform (e.g., PaaS, SaaS, IaaS, MLOps Platform, Data Platform).', example: 'MLOps Platform' }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "platformType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key services offered by the platform.',
        type: 'array',
        items: { type: 'string' },
        example: ['Model Training & Experimentation', 'Automated Deployment Pipelines', 'Data Versioning & Lineage', 'Monitoring & Observability'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "keyServices", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "URL to the platform's main documentation.", example: 'https://platform.example.com/docs' }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "documentationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pricing model of the platform (e.g., Subscription, Usage-based, Free Tier Available).', example: 'Usage-based with enterprise tiers' }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "pricingModel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the Service Level Agreement (SLA) documentation.', example: 'https://platform.example.com/sla' }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "slaUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Geographical regions where the platform services are available.',
        type: 'array',
        items: { type: 'string' },
        example: ['us-east-1', 'eu-west-2', 'ap-southeast-1'],
    }),
    __metadata("design:type", Object)
], PlatformDetailsResponseDto.prototype, "supportedRegions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the platform details were created', example: '2024-04-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], PlatformDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the platform details', example: '2024-04-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], PlatformDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=platform-details-response.dto.js.map