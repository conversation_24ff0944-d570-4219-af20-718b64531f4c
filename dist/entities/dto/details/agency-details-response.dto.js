"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgencyDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AgencyDetailsResponseDto {
}
exports.AgencyDetailsResponseDto = AgencyDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'e5f6g7h8-i9j0-1234-5678-901234abcdef' }),
    __metadata("design:type", String)
], AgencyDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Services offered by the agency.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Strategy Consulting', 'Custom Model Development', 'Data Annotation'],
    }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "servicesOffered", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Primary industries the agency focuses on.',
        type: 'array',
        items: { type: 'string' },
        example: ['Healthcare', 'Finance', 'E-commerce'],
    }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "industryFocus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Typical size of clients the agency works with (e.g., startups, SMEs, enterprise).',
        type: 'array',
        items: { type: 'string' },
        example: ['SMEs', 'Enterprise'],
    }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "targetClientSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Specific target audience or roles the agency serves.',
        type: 'array',
        items: { type: 'string' },
        example: ['CTOs', 'Product Managers'],
    }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Summary of the agency\'s location(s) or service areas.', example: 'Global, with offices in New York and London' }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "locationSummary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the agency\'s portfolio or case studies.', example: 'https://example.agency/portfolio' }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "portfolioUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Information about the agency\'s pricing structure or engagement models.', example: 'Project-based, Retainer options available' }),
    __metadata("design:type", Object)
], AgencyDetailsResponseDto.prototype, "pricingInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the agency details were created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], AgencyDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the agency details', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], AgencyDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=agency-details-response.dto.js.map