"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectReferenceDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ProjectReferenceDetailsResponseDto {
}
exports.ProjectReferenceDetailsResponseDto = ProjectReferenceDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'e1f2g3h4-i5j6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], ProjectReferenceDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current status of the project (e.g., active, completed, archived, proof-of-concept).', example: 'active' }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "projectStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the source code repository.', example: 'https://github.com/user/project' }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "sourceCodeUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to a live demo of the project.', example: 'https://project-demo.example.com' }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "liveDemoUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Technologies used in the project.',
        type: 'array',
        items: { type: 'string' },
        example: ['React', 'Node.js', 'PostgreSQL', 'Docker'],
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "technologies", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Main goals or objectives of the project.', example: 'To demonstrate real-time data visualization.' }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "projectGoals", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of contributors or team members.',
        type: 'array',
        items: { type: 'string' },
        example: ['Jane Doe', 'John Smith'],
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "contributors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the project reference details were created', example: '2023-05-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], ProjectReferenceDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the project reference details', example: '2023-05-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], ProjectReferenceDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=project-reference-details-response.dto.js.map