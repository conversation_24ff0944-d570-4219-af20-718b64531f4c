"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HardwareDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class HardwareDetailsResponseDto {
}
exports.HardwareDetailsResponseDto = HardwareDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'l1m2n3o4-p5q6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], HardwareDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of hardware (e.g., GPU, FPGA, ASIC, TPU, AI Accelerator).', example: 'GPU' }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "hardwareType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key specifications of the hardware.',
        type: 'object',
        additionalProperties: true,
        example: { memory: '24GB GDDR6X', cuda_cores: 10496, tflops: 35.6, power_consumption: '350W' },
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "specifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Manufacturer of the hardware.', example: 'NVIDIA' }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "manufacturer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Release date of the hardware.', example: '2023-03-15', type: String, format: 'date' }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "releaseDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Typical price range for the hardware.', example: '$699 - $999' }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the hardware datasheet or product page.', example: 'https://nvidia.com/products/gpu/rtx4080' }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "datasheetUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the hardware details were created', example: '2023-12-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], HardwareDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the hardware details', example: '2023-12-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], HardwareDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=hardware-details-response.dto.js.map