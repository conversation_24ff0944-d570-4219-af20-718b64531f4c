"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PodcastDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PodcastDetailsResponseDto {
}
exports.PodcastDetailsResponseDto = PodcastDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'o1p2q3r4-s5t6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], PodcastDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Host(s) of the podcast.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Enthusiast A', 'Tech Podcaster B'],
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "hostNames", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Average length of a podcast episode.', example: '45 minutes' }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "averageEpisodeLength", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Main topics covered by the podcast.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Ethics', 'Machine Learning Research', 'Future of Technology'],
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "mainTopics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to listen to the podcast (e.g., Spotify, Apple Podcasts).', example: 'https://open.spotify.com/show/podcast123' }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "listenUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Frequency of new podcast episodes (e.g., Weekly, Bi-weekly).', example: 'Weekly' }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary language of the podcast.', example: 'English', default: 'English' }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "primaryLanguage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the podcast details were created', example: '2024-03-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], PodcastDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the podcast details', example: '2024-03-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], PodcastDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=podcast-details-response.dto.js.map