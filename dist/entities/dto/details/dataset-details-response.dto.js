"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatasetDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DatasetDetailsResponseDto {
}
exports.DatasetDetailsResponseDto = DatasetDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], DatasetDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Format of the dataset (e.g., CSV, JSON, Parquet).', example: 'CSV' }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the source of the dataset.', example: 'https://example.com/dataset.csv' }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "sourceUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'License under which the dataset is released (e.g., MIT, CC BY 4.0).', example: 'CC BY 4.0' }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "license", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Size of the dataset in bytes.', example: 10485760, type: 'integer', format: 'int64' }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "sizeInBytes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'A brief description of the dataset.', example: 'A collection of anonymized user interactions.' }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Notes on how to access or use the dataset.', example: 'Requires authentication token for download.' }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "accessNotes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the dataset details were created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], DatasetDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the dataset details', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], DatasetDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=dataset-details-response.dto.js.map