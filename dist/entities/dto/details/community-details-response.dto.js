"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommunityDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CommunityDetailsResponseDto {
}
exports.CommunityDetailsResponseDto = CommunityDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'g7h8i9j0-k1l2-3456-7890-123456abcdef' }),
    __metadata("design:type", String)
], CommunityDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Platform where the community is hosted (e.g., Discord, Slack, Discourse).', example: 'Discord' }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of members in the community.', example: 5000, type: Number }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "memberCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Main topics or focus areas of the community.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Safety', 'LLM Development', 'Open Source AI'],
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "focusTopics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the community rules or guidelines.', example: 'https://example.com/community/rules' }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "rulesUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to join or get an invite to the community.', example: 'https://discord.gg/communityXyz' }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "inviteUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the main channel or landing page of the community.', example: 'https://example.com/community/main' }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "mainChannelUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the community details were created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], CommunityDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the community details', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], CommunityDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=community-details-response.dto.js.map