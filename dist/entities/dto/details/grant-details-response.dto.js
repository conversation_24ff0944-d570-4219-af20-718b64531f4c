"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrantDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class GrantDetailsResponseDto {
}
exports.GrantDetailsResponseDto = GrantDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'j1k2l3m4-n5o6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], GrantDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the institution or organization providing the grant.', example: 'AI Research Foundation' }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "grantingInstitution", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Eligibility criteria for the grant.', example: 'PhD students in AI ethics, Postdoctoral researchers in NLP.' }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "eligibilityCriteria", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Application deadline for the grant.', example: '2024-12-31', type: String, format: 'date' }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "applicationDeadline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Funding amount or range for the grant (e.g., $10,000 - $50,000).', example: 'Up to $25,000' }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "fundingAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the grant application page or information page.', example: 'https://airesearchfoundation.org/grants/apply' }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "applicationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Specific focus area of the grant.', example: 'AI for Social Good' }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "grantFocusArea", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the grant details were created', example: '2023-10-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], GrantDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the grant details', example: '2023-10-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], GrantDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=grant-details-response.dto.js.map