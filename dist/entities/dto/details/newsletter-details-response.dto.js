"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsletterDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class NewsletterDetailsResponseDto {
}
exports.NewsletterDetailsResponseDto = NewsletterDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'h8i9j0k1-l2m3-4567-8901-234567abcdef' }),
    __metadata("design:type", String)
], NewsletterDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Frequency of the newsletter publication (e.g., Daily, Weekly, Monthly).', example: 'Weekly' }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Main topics covered in the newsletter.',
        type: 'array',
        items: { type: 'string' },
        example: ['Latest AI Breakthroughs', 'Tool Reviews', 'Industry News'],
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "mainTopics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the newsletter\'s archive.', example: 'https://example.com/newsletter/archive' }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "archiveUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to subscribe to the newsletter.', example: 'https://example.com/newsletter/subscribe' }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "subscribeUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the author or publisher.', example: 'AI Insights Team' }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "authorName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of subscribers.', example: 12000, type: Number }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "subscriberCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the newsletter details were created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], NewsletterDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the newsletter details', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], NewsletterDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=newsletter-details-response.dto.js.map