"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class JobDetailsResponseDto {
}
exports.JobDetailsResponseDto = JobDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'i1j2k3l4-m5n6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], JobDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Title of the job position.', example: 'AI Research Scientist' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "jobTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the company offering the job.', example: 'FutureTech AI Corp.' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Location type for the job (e.g., Remote, On-site, Hybrid).', example: 'Remote' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "locationType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Salary range for the position (e.g., $120k - $150k, Competitive).', example: '$130,000 - $160,000 USD' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "salaryRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the job application page.', example: 'https://futuretech.ai/careers/apply?jobId=123' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "applicationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Full description of the job role, responsibilities, and qualifications.', example: 'Seeking an experienced AI Research Scientist to lead innovative projects...' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "jobDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Required or preferred experience level (e.g., Entry, Mid, Senior).', example: 'Senior (5+ years)' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "experienceLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of employment (e.g., Full-time, Part-time, Contract).', example: 'Full-time' }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "employmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the job details were created', example: '2023-09-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], JobDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the job details', example: '2023-09-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], JobDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=job-details-response.dto.js.map