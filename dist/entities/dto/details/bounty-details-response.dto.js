"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BountyDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class BountyDetailsResponseDto {
}
exports.BountyDetailsResponseDto = BountyDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'k1l2m3n4-o5p6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], BountyDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Issuer of the bounty (e.g., company name, project name).', example: 'OpenSource Project X' }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "bountyIssuer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Reward amount for completing the bounty (e.g., 1000 USD, 0.5 ETH).', example: '500 USDC' }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "rewardAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Detailed requirements or tasks for the bounty.', example: 'Fix a critical bug in the authentication module. See issue #123.' }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "requirements", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Submission deadline for the bounty.', example: '2024-11-30', type: String, format: 'date' }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "submissionDeadline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the platform where the bounty is listed or managed (e.g., Gitcoin, HackerOne).', example: 'https://gitcoin.co/bounties/123' }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "platformUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Difficulty level of the bounty (e.g., Easy, Medium, Hard).', example: 'Medium' }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "difficultyLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the bounty details were created', example: '2023-11-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], BountyDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the bounty details', example: '2023-11-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], BountyDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=bounty-details-response.dto.js.map