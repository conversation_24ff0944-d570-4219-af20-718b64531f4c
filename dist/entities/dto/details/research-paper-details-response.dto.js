"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResearchPaperDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ResearchPaperDetailsResponseDto {
}
exports.ResearchPaperDetailsResponseDto = ResearchPaperDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'b1c2d3e4-f5g6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], ResearchPaperDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Date of publication.', example: '2023-05-15', type: String, format: 'date' }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "publicationDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Digital Object Identifier (DOI) of the research paper.', example: '10.1000/xyz123' }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "doi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of authors.',
        type: 'array',
        items: { type: 'string' },
        example: ['Dr. Alice Smith', 'Dr. Bob Johnson'],
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "authors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Abstract of the research paper.', example: 'This paper explores advanced techniques in machine learning...' }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "abstract", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the journal or conference where the paper was published.', example: 'Journal of AI Research' }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "journalOrConference", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the publication (e.g., on arXiv, publisher site)..', example: 'https://arxiv.org/abs/2305.12345' }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "publicationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of citations the paper has received.', example: 42, type: 'integer' }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "citationCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the research paper details were created', example: '2023-02-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], ResearchPaperDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the research paper details', example: '2023-02-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], ResearchPaperDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=research-paper-details-response.dto.js.map