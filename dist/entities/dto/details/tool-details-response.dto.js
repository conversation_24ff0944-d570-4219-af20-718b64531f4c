"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const prisma_1 = require("../../../../generated/prisma");
class ToolDetailsResponseDto {
}
exports.ToolDetailsResponseDto = ToolDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'd4e5f6g7-h8i9-0123-4567-890123abcdef' }),
    __metadata("design:type", String)
], ToolDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: prisma_1.TechnicalLevel, description: 'Recommended technical level for using the tool.', example: prisma_1.TechnicalLevel.INTERMEDIATE }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "technicalLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: prisma_1.LearningCurve, description: 'Perceived learning curve for the tool.', example: prisma_1.LearningCurve.MEDIUM }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "learningCurve", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target audience for the tool (e.g., developers, designers, marketers).',
        type: 'object',
        example: { developers: true, dataScientists: true },
        additionalProperties: true,
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Indicates if the tool has an API.', example: true, default: false }),
    __metadata("design:type", Boolean)
], ToolDetailsResponseDto.prototype, "hasApi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the API documentation.', example: 'https://example.com/api/docs' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "apiDocumentationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to an API sandbox environment.', example: 'https://sandbox.example.com/api' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "apiSandboxUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key features of the tool.',
        type: 'array',
        items: { type: 'string' },
        example: ['Feature A', 'Feature B'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "keyFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Common use cases for the tool.',
        type: 'array',
        items: { type: 'string' },
        example: ['Data analysis', 'Automation'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "useCases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: prisma_1.PricingModel, description: 'Pricing model of the tool.', example: prisma_1.PricingModel.FREEMIUM }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "pricingModel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: prisma_1.PriceRange, description: 'General price range of the tool.', example: prisma_1.PriceRange.MEDIUM }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Specific details about pricing.', example: 'Pro plan at $49/month.' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "pricingDetails", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the pricing page.', example: 'https://example.com/pricing' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "pricingUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Indicates if the tool has a free tier.', example: true, default: false }),
    __metadata("design:type", Boolean)
], ToolDetailsResponseDto.prototype, "hasFreeTier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Supported platforms (e.g., Web, Windows, macOS, Linux).',
        type: 'array',
        items: { type: 'string' },
        example: ['Web', 'Windows'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Known integrations with other tools or services.',
        type: 'array',
        items: { type: 'string' },
        example: ['Slack', 'Google Drive'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Programming languages supported or relevant to the tool.',
        type: 'array',
        items: { type: 'string' },
        example: ['Python', 'JavaScript'],
    }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "supportedLanguages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current version of the tool.', example: 'v2.5.1' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "currentVersion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Date of the last version update.', example: '2023-10-15T00:00:00.000Z', type: String, format: 'date-time' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "lastVersionUpdateDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the tool\'s changelog.', example: 'https://example.com/changelog' }),
    __metadata("design:type", Object)
], ToolDetailsResponseDto.prototype, "changelogUrl", void 0);
//# sourceMappingURL=tool-details-response.dto.js.map