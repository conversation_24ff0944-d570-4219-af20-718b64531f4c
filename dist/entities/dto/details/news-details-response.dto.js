"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class NewsDetailsResponseDto {
}
exports.NewsDetailsResponseDto = NewsDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'm1n2o3p4-q5r6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], NewsDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Publication date of the news article.', example: '2024-01-15', type: String, format: 'date' }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "publicationDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the source or publisher of the news.', example: 'TechCrunch' }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "sourceName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL to the original news article.', example: 'https://techcrunch.com/2024/01/15/ai-breakthrough/' }),
    __metadata("design:type", String)
], NewsDetailsResponseDto.prototype, "articleUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Author(s) of the news article.', example: 'Jane Journalist' }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'A brief summary or abstract of the news article.', example: 'A new AI model has achieved state-of-the-art results in image generation...' }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the news details were created', example: '2024-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], NewsDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the news details', example: '2024-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], NewsDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=news-details-response.dto.js.map