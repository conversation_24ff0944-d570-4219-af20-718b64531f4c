"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvestorDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class InvestorDetailsResponseDto {
}
exports.InvestorDetailsResponseDto = InvestorDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'g1h2i3j4-k5l6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], InvestorDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Areas of focus for investment.',
        type: 'array',
        items: { type: 'string' },
        example: ['Seed Stage AI', 'Healthcare Tech', 'B2B SaaS'],
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "investmentFocusAreas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "URL to the investor or firm's portfolio.", example: 'https://investor.com/portfolio' }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "portfolioUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Typical size of investment (e.g., $100k - $1M).', example: '$250k - $750k' }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "typicalInvestmentSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Investment stages the investor participates in.',
        type: 'array',
        items: { type: 'string' },
        example: ['Pre-seed', 'Seed', 'Series A'],
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "investmentStages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Contact email for inquiries or pitches.', example: '<EMAIL>' }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Preferred method of communication for initial contact.', example: 'Email introduction via mutual connection' }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "preferredCommunication", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the investor details were created', example: '2023-07-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], InvestorDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the investor details', example: '2023-07-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], InvestorDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=investor-details-response.dto.js.map