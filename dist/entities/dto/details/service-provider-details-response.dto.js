"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceProviderDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ServiceProviderDetailsResponseDto {
}
exports.ServiceProviderDetailsResponseDto = ServiceProviderDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'f1g2h3i4-j5k6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], ServiceProviderDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Areas of service provided.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Development', 'Data Science Consulting', 'MLOps Implementation'],
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "serviceAreas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to case studies or portfolio.', example: 'https://provider.com/case-studies' }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "caseStudiesUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to book a consultation.', example: 'https://provider.com/book-consultation' }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "consultationBookingUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Industries the service provider specializes in.',
        type: 'array',
        items: { type: 'string' },
        example: ['Healthcare', 'Finance', 'Retail'],
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "industrySpecializations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Typical size of companies the provider works with (e.g., Startups, SMEs, Enterprise).', example: 'SMEs' }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "companySizeFocus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Typical hourly rate range.', example: '$150 - $250' }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "hourlyRateRange", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the service provider details were created', example: '2023-06-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], ServiceProviderDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the service provider details', example: '2023-06-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], ServiceProviderDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=service-provider-details-response.dto.js.map