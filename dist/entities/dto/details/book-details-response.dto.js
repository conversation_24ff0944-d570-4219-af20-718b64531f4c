"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class BookDetailsResponseDto {
}
exports.BookDetailsResponseDto = BookDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'n1o2p3q4-r5s6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], BookDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Author(s) of the book.',
        type: 'array',
        items: { type: 'string' },
        example: ['Dr. AI Scholar', 'Prof. Data Insights'],
    }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "authorNames", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ISBN of the book.', example: '978-3-16-148410-0' }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "isbn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Publisher of the book.', example: 'AI Press International' }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "publisher", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Year the book was published.', example: 2023, type: 'integer' }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "publicationYear", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of pages in the book.', example: 350, type: 'integer' }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "pageCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'A brief summary or abstract of the book.', example: 'A comprehensive guide to modern AI techniques and applications...' }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to purchase or access the book.', example: 'https://example.com/books/ai-guide' }),
    __metadata("design:type", Object)
], BookDetailsResponseDto.prototype, "purchaseUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the book details were created', example: '2024-02-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], BookDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the book details', example: '2024-02-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], BookDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=book-details-response.dto.js.map