"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ModelDetailsResponseDto {
}
exports.ModelDetailsResponseDto = ModelDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'd1e2f3g4-h5i6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], ModelDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Architecture of the model (e.g., Transformer, CNN, RNN).', example: 'Transformer' }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "modelArchitecture", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of parameters in the model.', example: 175000000000, type: 'integer', format: 'int64' }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "parametersCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Information about the dataset used for training the model.', example: 'Trained on BookCorpus and Wikipedia.' }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "trainingDataset", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Performance metrics of the model.',
        type: 'object',
        additionalProperties: { type: 'number' },
        example: { accuracy: 0.95, f1_score: 0.92, perplexity: 8.5 },
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "performanceMetrics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to download or access the model (e.g., Hugging Face Hub)..', example: 'https://huggingface.co/org/model' }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "modelUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'License of the model (e.g., Apache 2.0, MIT).', example: 'Apache 2.0' }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "license", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the model details were created', example: '2023-04-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], ModelDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the model details', example: '2023-04-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], ModelDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=model-details-response.dto.js.map