"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftwareDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class SoftwareDetailsResponseDto {
}
exports.SoftwareDetailsResponseDto = SoftwareDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'c1d2e3f4-g5h6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], SoftwareDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the software repository (e.g., GitHub, GitLab)..', example: 'https://github.com/user/repo' }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "repositoryUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of license for the software (e.g., MIT, GPLv3).', example: 'MIT' }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "licenseType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Programming languages used or supported by the software.',
        type: 'array',
        items: { type: 'string' },
        example: ['Python', 'JavaScript', 'C++'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "programmingLanguages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Platforms the software is compatible with.',
        type: 'array',
        items: { type: 'string' },
        example: ['Windows', 'Linux', 'macOS', 'Web'],
    }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "platformCompatibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current version of the software.', example: '1.2.3' }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "currentVersion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Release date of the current version.', example: '2023-04-01', type: String, format: 'date' }),
    __metadata("design:type", Object)
], SoftwareDetailsResponseDto.prototype, "releaseDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the software details were created', example: '2023-03-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], SoftwareDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the software details', example: '2023-03-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], SoftwareDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=software-details-response.dto.js.map