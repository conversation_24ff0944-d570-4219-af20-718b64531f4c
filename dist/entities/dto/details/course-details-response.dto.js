"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const prisma_1 = require("../../../../generated/prisma");
class CourseDetailsResponseDto {
}
exports.CourseDetailsResponseDto = CourseDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'i9j0k1l2-m3n4-5678-9012-345678abcdef' }),
    __metadata("design:type", String)
], CourseDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the instructor or course provider.', example: 'Dr. AI Expert' }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "instructorName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Estimated duration to complete the course.', example: 'Approx. 40 hours' }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "durationText", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: prisma_1.SkillLevel, description: 'Recommended skill level for the course.', example: prisma_1.SkillLevel.BEGINNER }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "skillLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Any prerequisites for taking the course.', example: 'Basic understanding of Python.' }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "prerequisites", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the course syllabus or detailed outline.', example: 'https://example.com/course/syllabus' }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "syllabusUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of students enrolled in the course.', example: 2500, type: Number }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "enrollmentCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if a certificate is available upon completion.', example: true, default: false }),
    __metadata("design:type", Object)
], CourseDetailsResponseDto.prototype, "certificateAvailable", void 0);
//# sourceMappingURL=course-details-response.dto.js.map