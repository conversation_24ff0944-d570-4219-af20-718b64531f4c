"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class EventDetailsResponseDto {
}
exports.EventDetailsResponseDto = EventDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'h1i2j3k4-l5m6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], EventDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of the event (e.g., Conference, Webinar, Workshop, Meetup).', example: 'Conference' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "eventType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Start date and time of the event.', example: '2024-09-15T09:00:00.000Z', type: String, format: 'date-time' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'End date and time of the event.', example: '2024-09-17T17:00:00.000Z', type: String, format: 'date-time' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Location of the event (can be a physical address or \'Online\').', example: 'Online' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL for event registration.', example: 'https://example.com/event/register' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "registrationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of speakers or key presenters at the event.',
        type: 'array',
        items: { type: 'string' },
        example: ['Dr. AI Visionary', 'Prof. ML Innovator'],
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "speakerList", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the event agenda or schedule.', example: 'https://example.com/event/agenda' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "agendaUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pricing information for the event (e.g., Free, $99, Contact for enterprise).', example: '$99' }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the event details were created', example: '2023-08-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], EventDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the event details', example: '2023-08-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], EventDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=event-details-response.dto.js.map