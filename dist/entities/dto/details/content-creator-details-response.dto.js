"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentCreatorDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ContentCreatorDetailsResponseDto {
}
exports.ContentCreatorDetailsResponseDto = ContentCreatorDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'f6g7h8i9-j0k1-2345-6789-012345abcdef' }),
    __metadata("design:type", String)
], ContentCreatorDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the content creator or channel.', example: 'AI Explained Today' }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "creatorName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary platform where the content is published (e.g., YouTube, Twitch, Blog).', example: 'YouTube' }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "primaryPlatform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Main focus areas or topics covered by the content creator.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI News', 'Machine Learning Tutorials', 'Tech Reviews'],
    }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "focusAreas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of followers or subscribers.', example: 150000, type: Number }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "followerCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to an example piece of content or the main channel/profile.', example: 'https://youtube.com/c/AIExplainedToday' }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "exampleContentUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the content creator details were created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], ContentCreatorDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the content creator details', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], ContentCreatorDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=content-creator-details-response.dto.js.map