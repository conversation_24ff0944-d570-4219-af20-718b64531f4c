import { TechnicalLevel, LearningCurve, PricingModel, PriceRange } from '../../../../generated/prisma';
export declare class ToolDetailsResponseDto {
    entityId: string;
    technicalLevel?: TechnicalLevel | null;
    learningCurve?: LearningCurve | null;
    targetAudience?: any | null;
    hasApi: boolean;
    apiDocumentationUrl?: string | null;
    apiSandboxUrl?: string | null;
    keyFeatures?: any | null;
    useCases?: any | null;
    pricingModel?: PricingModel | null;
    priceRange?: PriceRange | null;
    pricingDetails?: string | null;
    pricingUrl?: string | null;
    hasFreeTier: boolean;
    platforms?: any | null;
    integrations?: any | null;
    supportedLanguages?: any | null;
    currentVersion?: string | null;
    lastVersionUpdateDate?: Date | null;
    changelogUrl?: string | null;
}
