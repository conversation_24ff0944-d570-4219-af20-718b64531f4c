import { EntityStatus, Prisma } from 'generated/prisma';
export declare class ListEntitiesDto {
    page?: number;
    limit?: number;
    status?: EntityStatus;
    entityTypeIds?: string[];
    categoryIds?: string[];
    tagIds?: string[];
    featureIds?: string[];
    searchTerm?: string;
    sortBy?: string;
    sortOrder?: Prisma.SortOrder;
    submitterId?: string;
    createdAtFrom?: Date;
    createdAtTo?: Date;
}
