import { EntityStatus, AffiliateStatus } from 'generated/prisma';
import { CreateToolDetailsDto } from './details/create-tool-details.dto';
import { CreateCourseDetailsDto } from './details/create-course-details.dto';
import { CreateAgencyDetailsDto } from './details/create-agency-details.dto';
import { CreateContentCreatorDetailsDto } from './details/create-content-creator-details.dto';
import { CreateCommunityDetailsDto } from './details/create-community-details.dto';
import { CreateNewsletterDetailsDto } from './details/create-newsletter-details.dto';
import { CreateDatasetDetailsDto } from './details/create-dataset-details.dto';
import { CreateResearchPaperDetailsDto } from './details/create-research-paper-details.dto';
import { CreateSoftwareDetailsDto } from './details/create-software-details.dto';
import { CreateModelDetailsDto } from './details/create-model-details.dto';
import { CreateProjectReferenceDetailsDto } from './details/create-project-reference-details.dto';
import { CreateServiceProviderDetailsDto } from './details/create-service-provider-details.dto';
import { CreateInvestorDetailsDto } from './details/create-investor-details.dto';
import { CreateEventDetailsDto } from './details/create-event-details.dto';
import { CreateJobDetailsDto } from './details/create-job-details.dto';
import { CreateGrantDetailsDto } from './details/create-grant-details.dto';
import { CreateBountyDetailsDto } from './details/create-bounty-details.dto';
import { CreateHardwareDetailsDto } from './details/create-hardware-details.dto';
import { CreateNewsDetailsDto } from './details/create-news-details.dto';
import { CreateBookDetailsDto } from './details/create-book-details.dto';
import { CreatePodcastDetailsDto } from './details/create-podcast-details.dto';
import { CreatePlatformDetailsDto } from './details/create-platform-details.dto';
export declare class CreateEntityDto {
    name: string;
    website_url: string;
    entity_type_id: string;
    short_description?: string;
    description?: string;
    logo_url?: string;
    documentation_url?: string;
    contact_url?: string;
    privacy_policy_url?: string;
    founded_year?: number;
    social_links?: Record<string, any>;
    category_ids?: string[];
    tag_ids?: string[];
    feature_ids?: string[];
    meta_title?: string;
    meta_description?: string;
    employee_count_range?: string;
    funding_stage?: string;
    location_summary?: string;
    ref_link?: string;
    affiliate_status?: AffiliateStatus;
    scraped_review_sentiment_label?: string;
    scraped_review_sentiment_score?: number;
    scraped_review_count?: number;
    status?: EntityStatus;
    tool_details?: CreateToolDetailsDto;
    course_details?: CreateCourseDetailsDto;
    agency_details?: CreateAgencyDetailsDto;
    content_creator_details?: CreateContentCreatorDetailsDto;
    community_details?: CreateCommunityDetailsDto;
    newsletter_details?: CreateNewsletterDetailsDto;
    dataset_details?: CreateDatasetDetailsDto;
    research_paper_details?: CreateResearchPaperDetailsDto;
    software_details?: CreateSoftwareDetailsDto;
    model_details?: CreateModelDetailsDto;
    project_reference_details?: CreateProjectReferenceDetailsDto;
    service_provider_details?: CreateServiceProviderDetailsDto;
    investor_details?: CreateInvestorDetailsDto;
    event_details?: CreateEventDetailsDto;
    job_details?: CreateJobDetailsDto;
    grant_details?: CreateGrantDetailsDto;
    bounty_details?: CreateBountyDetailsDto;
    hardware_details?: CreateHardwareDetailsDto;
    news_details?: CreateNewsDetailsDto;
    book_details?: CreateBookDetailsDto;
    podcast_details?: CreatePodcastDetailsDto;
    platform_details?: CreatePlatformDetailsDto;
}
