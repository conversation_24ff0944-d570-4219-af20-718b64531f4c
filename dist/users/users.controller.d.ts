import { UserService } from './users.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { User as UserModel } from '../../generated/prisma';
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { UserNotificationSettingsResponseDto } from './dto/user-notification-settings-response.dto';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    getMyProfile(user: UserModel): Promise<UserProfileResponseDto>;
    updateMyProfile(user: UserModel, updateProfileDto: UpdateProfileDto): Promise<UserProfileResponseDto>;
    getMyNotificationSettings(user: UserModel): Promise<UserNotificationSettingsResponseDto | null>;
    updateMyNotificationSettings(user: UserModel, updateDto: UpdateNotificationSettingsDto): Promise<UserNotificationSettingsResponseDto>;
    softDeleteMyAccount(user: UserModel): Promise<{
        message: string;
    }>;
}
