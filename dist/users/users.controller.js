"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const update_profile_dto_1 = require("./dto/update-profile.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const update_notification_settings_dto_1 = require("./dto/update-notification-settings.dto");
const swagger_1 = require("@nestjs/swagger");
const user_profile_response_dto_1 = require("./dto/user-profile-response.dto");
const user_notification_settings_response_dto_1 = require("./dto/user-notification-settings-response.dto");
let UserController = class UserController {
    constructor(userService) {
        this.userService = userService;
    }
    async getMyProfile(user) {
        const profile = await this.userService.findProfileById(user.id);
        if (!profile) {
            throw new common_1.NotFoundException('User profile not found. User might not exist in public table or JWT strategy is incorrect.');
        }
        return {
            id: profile.id,
            authUserId: profile.authUserId,
            email: profile.email,
            username: profile.username,
            displayName: profile.displayName,
            profilePictureUrl: profile.profilePictureUrl,
            bio: profile.bio,
            status: profile.status,
            role: profile.role,
            createdAt: profile.createdAt,
            updatedAt: profile.updatedAt,
            lastLoginAt: profile.lastLogin,
        };
    }
    async updateMyProfile(user, updateProfileDto) {
        const updatedProfile = await this.userService.updateProfile(user.id, updateProfileDto);
        return {
            id: updatedProfile.id,
            authUserId: updatedProfile.authUserId,
            email: updatedProfile.email,
            username: updatedProfile.username,
            displayName: updatedProfile.displayName,
            profilePictureUrl: updatedProfile.profilePictureUrl,
            bio: updatedProfile.bio,
            status: updatedProfile.status,
            role: updatedProfile.role,
            createdAt: updatedProfile.createdAt,
            updatedAt: updatedProfile.updatedAt,
            lastLoginAt: updatedProfile.lastLogin,
        };
    }
    async getMyNotificationSettings(user) {
        const settings = await this.userService.findNotificationSettingsByUserId(user.id);
        if (!settings)
            return null;
        return {
            id: settings.userId,
            userId: settings.userId,
            newCommentOnEntity: settings.emailNewEntityInFollowedCategory,
            replyToComment: settings.emailNewsletter,
            entityStatusChange: settings.emailUpdatesOnSavedEntity,
            newReviewOnEntity: settings.emailNewReviewOnSavedEntity,
            platformAnnouncements: settings.emailNewsletter,
            digestSubscription: settings.emailNewsletter,
            createdAt: settings.createdAt,
            updatedAt: settings.updatedAt,
        };
    }
    async updateMyNotificationSettings(user, updateDto) {
        const updatedSettings = await this.userService.updateNotificationSettings(user.id, updateDto);
        return {
            id: updatedSettings.userId,
            userId: updatedSettings.userId,
            newCommentOnEntity: updatedSettings.emailNewEntityInFollowedCategory,
            replyToComment: updatedSettings.emailNewsletter,
            entityStatusChange: updatedSettings.emailUpdatesOnSavedEntity,
            newReviewOnEntity: updatedSettings.emailNewReviewOnSavedEntity,
            platformAnnouncements: updatedSettings.emailNewsletter,
            digestSubscription: updatedSettings.emailNewsletter,
            createdAt: updatedSettings.createdAt,
            updatedAt: updatedSettings.updatedAt,
        };
    }
    async softDeleteMyAccount(user) {
        await this.userService.softDeleteUser(user.id, user.authUserId);
        return { message: 'Account successfully scheduled for deletion. All related data will be processed according to our retention policies.' };
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get current authenticated user\'s profile",
        description: "Retrieves the detailed profile information for the currently logged-in user."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Current user\'s profile retrieved successfully.", type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'User profile not found in the database. This might indicate an issue with data consistency or if the user was recently deleted.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMyProfile", null);
__decorate([
    (0, common_1.Put)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Update current authenticated user\'s profile",
        description: "Allows the currently logged-in user to update their profile information. Certain fields like email, role, or status might be restricted or handled by separate processes."
    }),
    (0, swagger_1.ApiBody)({ type: update_profile_dto_1.UpdateProfileDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "User profile updated successfully.", type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., username format, length constraints) or username conflict if attempting to change to an existing username.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'User not found during update attempt.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_profile_dto_1.UpdateProfileDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateMyProfile", null);
__decorate([
    (0, common_1.Get)('me/preferences'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Get current authenticated user\'s notification preferences",
        description: "Retrieves the notification settings for the currently logged-in user."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Notification preferences retrieved successfully.", type: user_notification_settings_response_dto_1.UserNotificationSettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Notification settings not found for this user (they may not have been created yet).' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMyNotificationSettings", null);
__decorate([
    (0, common_1.Put)('me/preferences'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: "Update current authenticated user\'s notification preferences",
        description: "Allows the currently logged-in user to update their notification settings. Settings are typically created on first update if they don\'t exist."
    }),
    (0, swagger_1.ApiBody)({ type: update_notification_settings_dto_1.UpdateNotificationSettingsDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: "Notification preferences updated successfully.", type: user_notification_settings_response_dto_1.UserNotificationSettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data for notification settings.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_notification_settings_dto_1.UpdateNotificationSettingsDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateMyNotificationSettings", null);
__decorate([
    (0, common_1.Delete)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: "Soft delete current authenticated user\'s account",
        description: "Marks the current user\'s account for deletion. The actual deletion and data purging process might be asynchronous and subject to retention policies. This action requires the user\'s current JWT."
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Account successfully scheduled for deletion.', schema: { example: { message: 'Account successfully scheduled for deletion. ...' } } }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated. A valid JWT is required.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Failed to process account deletion due to an internal error.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "softDeleteMyAccount", null);
exports.UserController = UserController = __decorate([
    (0, swagger_1.ApiTags)('Current User'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UserService])
], UserController);
//# sourceMappingURL=users.controller.js.map