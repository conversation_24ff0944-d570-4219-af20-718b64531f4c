"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma/index.js");
const prisma_2 = require("../../generated/prisma/index.js");
const supabase_service_1 = require("../supabase/supabase.service");
let UserService = class UserService {
    constructor(prismaService, supabaseService) {
        this.prismaService = prismaService;
        this.supabaseService = supabaseService;
    }
    async findProfileById(userId) {
        return this.prismaService.user.findUnique({
            where: { id: userId },
        });
    }
    async updateProfile(userId, dto) {
        if (dto.username) {
            const existingUserWithUsername = await this.prismaService.user.findFirst({
                where: {
                    username: dto.username,
                    NOT: { id: userId },
                },
            });
            if (existingUserWithUsername) {
                throw new common_1.ConflictException('Username already taken. Please choose another.');
            }
        }
        const dataToUpdate = {};
        if (dto.display_name !== undefined)
            dataToUpdate.displayName = dto.display_name;
        if (dto.username !== undefined)
            dataToUpdate.username = dto.username;
        if (dto.bio !== undefined)
            dataToUpdate.bio = dto.bio;
        if (dto.profile_picture_url !== undefined)
            dataToUpdate.profilePictureUrl = dto.profile_picture_url;
        if (dto.social_links !== undefined)
            dataToUpdate.socialLinks = dto.social_links;
        if (dto.technical_level !== undefined)
            dataToUpdate.technicalLevel = dto.technical_level;
        if (Object.keys(dataToUpdate).length === 0) {
            const currentUser = await this.findProfileById(userId);
            if (!currentUser)
                throw new common_1.NotFoundException('User not found during update.');
            return currentUser;
        }
        try {
            const updatedProfile = await this.prismaService.user.update({
                where: { id: userId },
                data: dataToUpdate,
            });
            return updatedProfile;
        }
        catch (error) {
            console.error("Error updating user profile:", error);
            if (error instanceof prisma_2.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                throw new common_1.NotFoundException(`User with ID ${userId} not found.`);
            }
            throw error;
        }
    }
    async findNotificationSettingsByUserId(userId) {
        return this.prismaService.userNotificationSettings.findUnique({
            where: { userId },
        });
    }
    async updateNotificationSettings(userId, dto) {
        const dataToUpdate = {};
        if (dto.emailNewsletter !== undefined)
            dataToUpdate.emailNewsletter = dto.emailNewsletter;
        if (dto.emailNewEntityInFollowedCategory !== undefined)
            dataToUpdate.emailNewEntityInFollowedCategory = dto.emailNewEntityInFollowedCategory;
        if (dto.emailNewEntityInFollowedTag !== undefined)
            dataToUpdate.emailNewEntityInFollowedTag = dto.emailNewEntityInFollowedTag;
        if (dto.emailNewReviewOnSavedEntity !== undefined)
            dataToUpdate.emailNewReviewOnSavedEntity = dto.emailNewReviewOnSavedEntity;
        if (dto.emailUpdatesOnSavedEntity !== undefined)
            dataToUpdate.emailUpdatesOnSavedEntity = dto.emailUpdatesOnSavedEntity;
        return this.prismaService.userNotificationSettings.upsert({
            where: { userId },
            update: dataToUpdate,
            create: {
                userId: userId,
                emailNewsletter: dto.emailNewsletter,
                emailNewEntityInFollowedCategory: dto.emailNewEntityInFollowedCategory,
                emailNewEntityInFollowedTag: dto.emailNewEntityInFollowedTag,
                emailNewReviewOnSavedEntity: dto.emailNewReviewOnSavedEntity,
                emailUpdatesOnSavedEntity: dto.emailUpdatesOnSavedEntity,
            },
        });
    }
    async softDeleteUser(userId, authUserId) {
        const anonymizedUsername = `deleted_user_${Date.now()}`;
        try {
            await this.prismaService.user.update({
                where: { id: userId },
                data: {
                    status: prisma_1.UserStatus.DELETED,
                    username: anonymizedUsername,
                    email: `${anonymizedUsername}@example.com`,
                    displayName: 'Deleted User',
                    profilePictureUrl: null,
                    bio: null,
                    socialLinks: prisma_2.Prisma.JsonNull,
                },
            });
            console.log(`Soft deleted user in public.users table with ID: ${userId}`);
        }
        catch (error) {
            console.error(`Error soft deleting user in public.users table with ID: ${userId}`, error);
            throw new common_1.InternalServerErrorException('Failed to update user profile during deletion process.');
        }
        try {
            const supabaseAdmin = this.supabaseService.getAdminClient();
            const { error: supabaseDeleteError } = await supabaseAdmin.auth.admin.deleteUser(authUserId);
            if (supabaseDeleteError) {
                console.error(`CRITICAL: Failed to delete Supabase auth user with ID: ${authUserId} after soft deleting profile.`, supabaseDeleteError);
                throw new common_1.InternalServerErrorException(`User profile was soft-deleted, but failed to delete authentication record. Please contact support. Error: ${supabaseDeleteError.message}`);
            }
            console.log(`Successfully deleted Supabase auth user with ID: ${authUserId}`);
        }
        catch (error) {
            console.error(`Exception during Supabase auth user deletion for ID: ${authUserId}`, error);
            throw new common_1.InternalServerErrorException('An unexpected error occurred while deleting the authentication record.');
        }
    }
    async findAllUsers(options) {
        const { page = 1, limit = 10, filterByStatus, sortBy = 'createdAt', sortOrder = 'desc' } = options;
        const skip = (page - 1) * limit;
        const where = {};
        if (filterByStatus) {
            where.status = filterByStatus;
        }
        const orderBy = {};
        if (sortBy) {
            orderBy[sortBy] = sortOrder;
        }
        const users = await this.prismaService.user.findMany({
            where,
            skip,
            take: limit,
            orderBy,
            select: {
                id: true,
                username: true,
                displayName: true,
                email: true,
                role: true,
                status: true,
                createdAt: true,
                updatedAt: true,
                lastLogin: true,
            }
        });
        const total = await this.prismaService.user.count({
            where,
        });
        return { users, total, page, limit };
    }
    async updateUserStatus(adminPerformingUpdate, targetUserId, newStatus) {
        if (adminPerformingUpdate.id === targetUserId) {
            throw new common_1.ForbiddenException('Admins cannot change their own status using this endpoint.');
        }
        if (newStatus === prisma_1.UserStatus.DELETED) {
            throw new common_1.BadRequestException('Cannot set status to DELETED using this endpoint. Please use the account deletion process.');
        }
        const targetUser = await this.prismaService.user.findUnique({
            where: { id: targetUserId },
        });
        if (!targetUser) {
            throw new common_1.NotFoundException(`User with ID ${targetUserId} not found.`);
        }
        try {
            return this.prismaService.user.update({
                where: { id: targetUserId },
                data: { status: newStatus },
            });
        }
        catch (error) {
            console.error(`Error updating status for user ${targetUserId}:`, error);
            throw new common_1.InternalServerErrorException('Failed to update user status.');
        }
    }
    async updateUserRole(adminPerformingUpdate, targetUserId, newRole) {
        if (adminPerformingUpdate.id === targetUserId) {
            throw new common_1.ForbiddenException('Admins cannot change their own role using this endpoint.');
        }
        const targetUser = await this.prismaService.user.findUnique({
            where: { id: targetUserId },
        });
        if (!targetUser) {
            throw new common_1.NotFoundException(`User with ID ${targetUserId} not found.`);
        }
        if (targetUser.role === prisma_1.UserRole.ADMIN && newRole !== prisma_1.UserRole.ADMIN) {
            const adminCount = await this.prismaService.user.count({
                where: { role: prisma_1.UserRole.ADMIN },
            });
            if (adminCount <= 1) {
                throw new common_1.ForbiddenException('Cannot remove the last admin role. At least one admin must exist.');
            }
        }
        try {
            return this.prismaService.user.update({
                where: { id: targetUserId },
                data: { role: newRole },
            });
        }
        catch (error) {
            console.error(`Error updating role for user ${targetUserId}:`, error);
            throw new common_1.InternalServerErrorException('Failed to update user role.');
        }
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, supabase_service_1.SupabaseService])
], UserService);
//# sourceMappingURL=users.service.js.map